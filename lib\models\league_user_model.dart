import 'package:cloud_firestore/cloud_firestore.dart';

class LeaguePlayerModel {
  String? username;
  String? league;
  int? score;
  String? playerId;
  Timestamp? lastUpdated; // Added lastUpdated field

  LeaguePlayerModel({
    this.username,
    this.league,
    this.score,
    this.playerId,
    this.lastUpdated,
  });

  factory LeaguePlayerModel.fromJson(Map<String, dynamic> json) {
    return LeaguePlayerModel(
      username: json['username'],
      league: json['league'],
      score: json['score'],
      playerId: json['playerId'],
      lastUpdated: json['lastUpdated'] as Timestamp?,
    );
  }

  Map<String, dynamic> toJson() => {
        'username': username,
        'league': league,
        'score': score,
        'playerId': playerId,
        'lastUpdated': lastUpdated,
      };

  static LeaguePlayerModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return LeaguePlayerModel.fromJson(snapshot);
  }
}
