import 'package:flutter/material.dart';
import 'package:get/get.dart';

Dialog dialogWidget(Widget widget) {
  return Dialog(
    backgroundColor: Colors.transparent,
    insetPadding: const EdgeInsets.all(10),
    child: Container(
        width: Get.width * 0.9,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
        ),
        child: widget),
  );
}
