import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:bibl/widgets/dialog_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AchievementUnlocked extends StatelessWidget {
  const AchievementUnlocked({super.key});

  @override
  Widget build(BuildContext context) {
    return dialogWidget(
      Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Txt(txt: 'Congratulations!', fontSize: 24),
          SvgPicture.asset(
            'assets/svgs/feedback.svg',
            height: 150,
          ),

          const SizedBox(height: 20),

          const Txt(txt: 'Achievement Unlocked!', fontSize: 20),

          const SizedBox(height: 10),
          const Txt(
            txt:
                'You have unlocked a new achievement! 🎉 Keep up the great work!',
            fontSize: 16,
            maxLines: 3,
            fontColor: Colors.grey,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 20),

          // Thumbs Up and Down Buttons
          buttonContainer(text: 'Continue')
        ],
      ),
    );
  }
}
