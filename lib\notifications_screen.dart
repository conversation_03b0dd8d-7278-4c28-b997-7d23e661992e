import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'widgets/customappbar.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final AuthController authController = Get.find();
  final ProfileController profileController = Get.find();

  setNotificationStatus(bool value) async {
    if (value) {
      profileController.isNotificationOn.value = true;
    } else {
      profileController.isNotificationOn.value = false;
    }
    setState(() {});
    await profileController.notificationUpdate();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        top: false,
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: CustomAppBar(
            widget: null,
            title: 'Notifikacije',
          ),
          body: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Row(
                //   children: [
                //     Txt(txt: 'Notifikacije', fontSize: 20),
                //     Spacer(),
                //     Txt(
                //       txt: 'Clear All',
                //       fontSize: 16,
                //       fontColor: Colors.grey,
                //     )
                //   ],
                // ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(
                      height: 20,
                    ),
                    SvgPicture.asset('assets/svgs/no_notification.svg'),
                  ],
                ),
                // Txt(
                //   txt: 'Today',
                //   fontSize: 16,
                //   fontColor: Colors.grey,
                // ),
                // Expanded(
                //   child: ListView.separated(
                //       itemBuilder: (context, index) {
                //         bool isExpanded = false;
                //         return notificationsTileWidget(isExpanded);
                //       },
                //       separatorBuilder: (context, index) {
                //         return SizedBox(
                //           height: 10,
                //         );
                //       },
                //       itemCount: 20),
                // )
              ],
            ),
          ),
        ));
  }

  Material notificationsTileWidget(bool isExpanded) {
    return Material(
      elevation: 1, // Elevation added here
      borderRadius: const BorderRadius.all(
        Radius.circular(30),
      ),
      child: GestureDetector(
        onTap: () {
          setState(() {
            isExpanded = !isExpanded;
          });
        },
        child: Container(
          decoration: BoxDecoration(
            color: isExpanded ? mainColor.withValues(alpha: 0.2) : Colors.white,
            borderRadius: const BorderRadius.all(
              Radius.circular(30),
            ),
          ),
          padding: const EdgeInsets.all(8.0),
          child: Padding(
            padding: const EdgeInsets.only(left: 10, right: 10),
            child: Row(children: [
              Image.asset(
                'assets/images/Logo Circle.png',
                height: 150,
              ),
              const SizedBox(
                width: 20,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Txt(
                    txt: '1 min ago',
                    fontSize: 16,
                    fontColor: Colors.grey,
                  ),
                  const Txt(
                    txt: 'You didn\'t enter any time yesterday',
                    fontSize: 18,
                    maxLines: 5,
                    fontColor: Colors.black,
                  ),
                  isExpanded
                      ? const Txt(
                          txt:
                              'You have completed your quiz. Keep up the great work and continue learning.',
                          fontSize: 16,
                          maxLines: 10,
                          fontColor: Colors.grey,
                        )
                      : const SizedBox.shrink()
                ],
              ),
            ]),
          ),
        ),
      ),
    );
  }
}
