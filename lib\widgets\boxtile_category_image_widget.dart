import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../controllers/category_controller.dart';
import '../res/style.dart';
import 'category_icon_widget.dart';

Builder boxTileCategoryImageBuilder(String category) {
  return Builder(
    builder: (context) {
      final CategoryController categoryController = Get.find();
      String? imageLink = categoryController.getImageLink(category);

      if (imageLink != null) {
        // If an image link is available, use NetworkImage to load the image
        return CircleAvatar(
            backgroundColor: mainColor.withValues(alpha: 0.2),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: CustomCategoryIcon(
                  topicName: category,
                  topicPhotoLink: imageLink,
                  isSelected: true,
                ),
              ),
            ));
      } else {
        // Fallback to the default SVG if no image link is found
        return CircleAvatar(
          backgroundColor: mainColor.withValues(alpha: 0.2),
          child:
              SvgPicture.asset('assets/svgs/umetnost_icon.svg'), // Fallback SVG
        );
      }
    },
  );
}
