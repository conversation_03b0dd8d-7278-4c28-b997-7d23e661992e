import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../controllers/category_controller.dart';
import '../res/style.dart';

String? getAssetPath(String category) {
  const assetMap = {
    'Filmovi': 'assets/svgs/filmovi_icon.svg',
    'Istorija': 'assets/svgs/istorija_icon.svg',
    'Umetnost': 'assets/svgs/umetnost_icon.svg',
    'Nauka': 'assets/svgs/nauka_icon.svg',
    'Kosmos': 'assets/svgs/kosmos_icon.svg',
    'Igre': 'assets/svgs/igre_icon.svg',
    'Mitologija': 'assets/svgs/mitologica_icon.svg',
    'Muzika': 'assets/svgs/muzika_icon.svg',
    'Sport': 'assets/svgs/sport_icon.svg',
    'Geografija': 'assets/svgs/geography_icon.svg',
    'Matematika': 'assets/svgs/maths_icon.svg',
    'Filozofija': 'assets/svgs/filo_icon.svg',
    'Finansijska Pismenost': 'assets/svgs/finance_icon.svg',
    'Tehnologija': 'assets/svgs/technology_icon.svg',
  };
  return assetMap[category];
}

class CustomCategoryIcon extends StatelessWidget {
  final String topicName;
  final String topicPhotoLink;
  final bool isSelected; // To handle selected state

  const CustomCategoryIcon({
    Key? key,
    required this.topicName,
    required this.topicPhotoLink,
    this.isSelected = false,
  }) : super(key: key);

  /// Helper to build the icon with or without gradient
  Widget _buildIcon(Widget child) {
    return isSelected
        ? ShaderMask(
            shaderCallback: (Rect bounds) {
              return mainColorsGradient.createShader(bounds);
            },
            blendMode: BlendMode.srcIn,
            child: child,
          )
        : child;
  }

  @override
  Widget build(BuildContext context) {
    final CategoryController categoryController = CategoryController();

    String? assetPath = getAssetPath(topicName);

    if (assetPath != null) {
      // Load from local assets
      return _buildIcon(
        SvgPicture.asset(assetPath,
            height: 32,
            colorFilter: ColorFilter.mode(
                isSelected ? Colors.white : Colors.black, BlendMode.srcIn)),
      );
    } else {
      // Load from dynamic file source
      return FutureBuilder<File?>(
        future: categoryController.getSvgFile(topicPhotoLink),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Icon(Icons.hourglass_bottom);
          } else if (snapshot.hasError || snapshot.data == null) {
            return const Icon(Icons.error);
          } else {
            return _buildIcon(
              SvgPicture.file(
                snapshot.data!,
                height: 32,
                colorFilter: ColorFilter.mode(
                    isSelected ? Colors.white : Colors.black, BlendMode.srcIn),
              ),
            );
          }
        },
      );
    }
  }
}
