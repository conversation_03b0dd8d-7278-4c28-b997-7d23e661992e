import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/box_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'models/lesson_model.dart';
import 'models/quiz_model.dart';
import 'widgets/customappbar.dart';

class Saved extends StatefulWidget {
  const Saved({Key? key}) : super(key: key);

  @override
  State<Saved> createState() => _SavedState();
}

class _SavedState extends State<Saved> {
  final ProfileController profileController = Get.find();
  final TextEditingController searchController = TextEditingController();

  bool isLessonsSelected = true;
  String searchQuery = '';

  @override
  void initState() {
    super.initState();

    // Listen for changes in the search field
    searchController.addListener(() {
      setState(() {
        searchQuery = searchController.text.toLowerCase().trim();
      });
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        widget: null,
        isBackButton: false,
        title: 'Tvoja biblioteka',
      ),
      body: ScrollConfiguration(
        behavior: const ScrollBehavior(),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search Bar
                searchWidget(),
                const SizedBox(height: 16.0),
                // Tabs
                tabsWidget(),
                const SizedBox(height: 16.0),
                // Content Card
                isLessonsSelected ? savedLessonsWidget() : savedQuizesWidget(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget tabsWidget() {
    return Container(
      decoration: BoxDecoration(
        color: grey3Color,
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(100),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => isLessonsSelected = true),
              child: Container(
                decoration: BoxDecoration(
                  color: isLessonsSelected ? Colors.white : grey3Color,
                  border: isLessonsSelected
                      ? Border.all(color: Colors.black.withValues(alpha: 0.2))
                      : null,
                  borderRadius: BorderRadius.circular(30.0),
                ),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Txt(
                      txt: 'Lekcije',
                      fontSize: 14,
                      fontColor: !isLessonsSelected ? grey2Color : Colors.black,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => setState(() => isLessonsSelected = false),
              child: Container(
                decoration: BoxDecoration(
                  color: !isLessonsSelected ? Colors.white : grey3Color,
                  border: !isLessonsSelected
                      ? Border.all(color: Colors.black.withValues(alpha: 0.2))
                      : null,
                  borderRadius: BorderRadius.circular(30.0),
                ),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Txt(
                      txt: 'Kvizovi',
                      fontSize: 14,
                      fontColor: isLessonsSelected ? grey2Color : Colors.black,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // LESSONS
  Widget savedLessonsWidget() {
    return Obx(() {
      // Get the list of saved lessons from the controller
      List<LessonModel> lessons = profileController.savedLessons;

      // Filter by the search query
      lessons = lessons
          .where((lesson) =>
              lesson.lessonName!.toLowerCase().contains(searchQuery))
          .toList();

      if (lessons.isEmpty) {
        return const SizedBox.shrink();
      }

      return ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: lessons.length,
        separatorBuilder: (_, __) => const SizedBox(height: 20),
        itemBuilder: (context, index) {
          return BoxWidget(lesson: lessons[index]);
        },
      );
    });
  }

  // QUIZZES (including Shuffle Quizzes)
  Widget savedQuizesWidget() {
    return Obx(() {
      // Regular quizzes
      List<QuizModel> quizzes = profileController.savedQuizzes;
      quizzes = quizzes
          .where((q) => q.quizName!.toLowerCase().contains(searchQuery))
          .toList();

      // Shuffle quizzes
      List<ShuffleQuizModel> shuffleQuizzes =
          profileController.savedShuffleQuizzes;
      shuffleQuizzes = shuffleQuizzes
          .where((sq) => sq.quizName!.toLowerCase().contains(searchQuery))
          .toList();

      // If everything is empty, show nothing
      if (quizzes.isEmpty && shuffleQuizzes.isEmpty) {
        return const SizedBox.shrink();
      }

      return Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: const Color(0xffF8EFFF), // light pink color
              borderRadius: BorderRadius.circular(16.0),
              border: Border.all(
                color: const Color(0xff9610FF).withValues(alpha: 0.5),
              ),
            ),
            child: const Txt(
              maxLines: 5,
              txt:
                  'Možete ponovo da odgovarate na isti kviz. Ako odgovarate na isti kviz u roku od 30 dana neuroni se računaju samo jednom.',
              fontWeight: FontWeight.normal,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 16),
          // Combine both in one ListView
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: quizzes.length + shuffleQuizzes.length,
            separatorBuilder: (_, __) => const SizedBox(height: 20),
            itemBuilder: (context, index) {
              if (index < quizzes.length) {
                return BoxWidget(quiz: quizzes[index]);
              } else {
                final shuffleIndex = index - quizzes.length;
                return BoxWidget(shuffleQuiz: shuffleQuizzes[shuffleIndex]);
              }
            },
          ),
        ],
      );
    });
  }

  Widget searchWidget() {
    return Container(
      height: 46,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(100),
      ),
      child: textFieldContainer(
        context,
        prefix: const Icon(Icons.search, color: grey2Color),
        controller: searchController,
        hint: 'Pretraga',
      ),
    );
  }
}
