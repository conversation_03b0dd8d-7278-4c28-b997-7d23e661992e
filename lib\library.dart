import 'package:bibl/models/lesson_model.dart';
import 'package:bibl/res/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fuzzywuzzy/fuzzywuzzy.dart';
import 'package:snowball_stemmer/snowball_stemmer.dart';
import 'controllers/lesson_controller.dart';
import 'models/quiz_model.dart';
import 'widgets/box_widget.dart';
import 'widgets/customappbar.dart';
import 'widgets/merged_items_list_widget.dart';

class Library extends StatefulWidget {
  final ScrollController scrollController;
  const Library({super.key, required this.scrollController});

  @override
  State<Library> createState() => _LibraryState();
}

class _LibraryState extends State<Library> {
  final ScrollController _scrollController = ScrollController();
  final searchController = TextEditingController();

  String searchText = '';

  @override
  void initState() {
    super.initState();
    searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    searchController.removeListener(_onSearchChanged);
    searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      searchText = searchController.text.trim().toLowerCase();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          title: 'Izaberi',
          isBackButton: false,
        ),
        body: ScrollConfiguration(
          behavior: const ScrollBehavior(),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
                child: textFieldContainer(context,
                    controller: searchController,
                    hint: 'Pretraga',
                    trailing: searchText.isNotEmpty
                        ? IconButton(
                            onPressed: () => searchController.clear(),
                            icon: const Icon(Icons.close))
                        : null,
                    prefix: const Icon(Icons.search)),
              ),
              // Search results for lessons, quizzes, and shuffle quizzes
              Expanded(
                  child: searchText.isEmpty
                      ? MergedItemsList(
                          isForLibrary: true,
                          scrollController: widget.scrollController,
                        )
                      : Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: _buildSearchResults(),
                        ))
            ],
          ),
        ));
  }

  Widget _buildSearchResults() {
    return FutureBuilder(
      future: _fetchSearchResults(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        }

        if (!snapshot.hasData || (snapshot.data as List).isEmpty) {
          return const Center(child: Text('No results found.'));
        }

        List<dynamic> results = snapshot.data as List<dynamic>;
        return ListView.separated(
          separatorBuilder: (context, index) => const SizedBox(height: 20),
          controller: _scrollController,
          shrinkWrap: true,
          itemCount: results.length,
          itemBuilder: (context, index) {
            var item = results[index];
            if (item is LessonModel) {
              return BoxWidget(lesson: item);
            } else if (item is QuizModel) {
              // print(item.)
              return BoxWidget(quiz: item);
            } else if (item is ShuffleQuizModel) {
              return BoxWidget(shuffleQuiz: item);
            }
            return const SizedBox.shrink();
          },
        );
      },
    );
  }

  Future<List<dynamic>> _fetchSearchResults() async {
    final stemmer = SnowballStemmer(Algorithm.serbian);
    final searchStem = stemmer.stem(searchText);
    final LessonController lessonController = Get.find();
    if (searchText.isEmpty) return [];

    // Create a list of items with their similarity scores
    List<MapEntry<dynamic, int>> itemsWithScores = [];

    for (var item in lessonController.allItems) {
      String itemName = '';
      if (item is LessonModel && item.lessonName != null) {
        itemName = item.lessonName!;
      } else if ((item is QuizModel || item is ShuffleQuizModel) &&
          item.quizName != null) {
        itemName = item.quizName!;
      }
      if (itemName.isNotEmpty) {
        final itemStem = stemmer.stem(itemName);
        if (searchStem == itemStem) {
          itemsWithScores.add(MapEntry(item, 100)); // Exact match
        } else {
          // Fallback to fuzzy matching on original forms with higher threshold
          int score = ratio(searchText.toLowerCase(), itemName.toLowerCase());
          int partialScore =
              partialRatio(searchText.toLowerCase(), itemName.toLowerCase());
          int tokenSortScore =
              tokenSortRatio(searchText.toLowerCase(), itemName.toLowerCase());
          int tokenSetScore =
              tokenSetRatio(searchText.toLowerCase(), itemName.toLowerCase());
          int finalScore = [score, partialScore, tokenSortScore, tokenSetScore]
              .reduce((a, b) => a > b ? a : b);
          if (finalScore >= 85) {
            // Increased threshold
            itemsWithScores.add(MapEntry(item, finalScore));
          }
        }
      }
    }
    // Sort by similarity score (highest first)
    itemsWithScores.sort((a, b) => b.value.compareTo(a.value));

    // Return the sorted items
    return itemsWithScores.map((entry) => entry.key).toList();
  }
}
