import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/profile_controller.dart';
import '../models/lesson_model.dart';
import '../models/quiz_model.dart';

class SavedLessonIconButton extends StatelessWidget {
  final LessonModel lesson;
  const SavedLessonIconButton({Key? key, required this.lesson})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController profileController = Get.find();

    return Obx(() {
      // Check if the lesson's ID is in the savedLessons list
      bool isSaved = profileController.savedLessons.any(
        (l) => l.lessonId == lesson.lessonId,
      );

      return IconButton(
        icon: isSaved
            ? const Icon(Icons.bookmark)
            : const Icon(Icons.bookmark_border_outlined),
        onPressed: () {
          if (isSaved) {
            profileController.deleteFromSavedLesson(
              savedLessonId: lesson.lessonId!,
            );
          } else {
            profileController.addToSavedLesson(savedLesson: lesson);
          }
        },
      );
    });
  }
}

class SavedQuizIconButton extends StatelessWidget {
  final QuizModel quiz;
  const SavedQuizIconButton({Key? key, required this.quiz}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController profileController = Get.find();

    return Obx(() {
      // Check if the quiz's ID is in the savedQuizzes list
      bool isSaved = profileController.savedQuizzes.any(
        (q) => q.quizId == quiz.quizId,
      );

      return IconButton(
        icon: isSaved
            ? const Icon(Icons.bookmark)
            : const Icon(Icons.bookmark_border_outlined),
        onPressed: () {
          if (isSaved) {
            profileController.deleteFromSavedQuizes(
              savedQuizId: quiz.quizId!,
            );
          } else {
            profileController.addToSavedQuizes(savedQuiz: quiz);
          }
        },
      );
    });
  }
}

class SavedShuffleQuizIconButton extends StatelessWidget {
  final ShuffleQuizModel quiz;
  const SavedShuffleQuizIconButton({Key? key, required this.quiz})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProfileController profileController = Get.find();

    return Obx(() {
      // Check if the shuffle quiz's ID is in the savedShuffleQuizzes list
      bool isSaved = profileController.savedShuffleQuizzes.any(
        (sq) => sq.quizId == quiz.quizId,
      );

      return IconButton(
        icon: isSaved
            ? const Icon(Icons.bookmark)
            : const Icon(Icons.bookmark_border_outlined),
        onPressed: () {
          if (isSaved) {
            profileController.deleteFromSavedShuffleQuizes(
              savedQuizId: quiz.quizId!,
            );
          } else {
            profileController.addToSavedShuffleQuizes(savedQuiz: quiz);
          }
        },
      );
    });
  }
}
