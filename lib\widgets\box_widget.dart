import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/services/reward_service.dart';
import 'package:bibl/services/lessonquiz_completion_service.dart';
import 'package:bibl/widgets/fav_icon_widget.dart';
import 'package:bibl/widgets/rewarded_ads_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/shuffle_quiz.dart';
import 'package:bibl/widgets/boxtile_category_image_widget.dart';
import '../lesson_intro.dart';
import '../models/lesson_model.dart';
import '../quiz.dart';
import 'image_carousel_widget.dart';
import 'saved_icon_widget.dart';
import '../services/share_item_service.dart';

class BoxWidget extends StatefulWidget {
  final LessonModel? lesson;
  final QuizModel? quiz;
  final ShuffleQuizModel? shuffleQuiz;
  const BoxWidget({
    super.key,
    this.lesson,
    this.quiz,
    this.shuffleQuiz,
  });

  @override
  State<BoxWidget> createState() => _BoxWidgetState();
}

class _BoxWidgetState extends State<BoxWidget> {
  void onTapp(
      {required Widget classs, required String id, required bool isCompleted}) {
    final ProfileController profileController = Get.find();
    RewardService rewardService =
        RewardService(profileController.userr.value.uid!);
    rewardService.addNeurons(20);

    // if (!profileController.userr.value.isPremiumUser!) {
    //   final completionService = LessonQuizCompletionService();
    //   bool isOpenedWithin30Days = completionService.isOpenedWithinThirtyDays(
    //       id, profileController.userr.value.openedQuizesAndArticlesinMonth);

    //   if (isOpenedWithin30Days || isCompleted) {
    //     Get.to(() => classs);
    //   } else {
    //     if (profileController.userr.value.hearts == 0) {
    //       Get.dialog(RewardedAdsDialog(classs: classs));
    //     } else {
    //       Get.to(() => classs);
    //       profileController.userr.value.hearts =
    //           profileController.userr.value.hearts! - 1;
    //       profileController.updateHearts(profileController.userr.value.hearts!);
    //     }
    //   }
    //   profileController.userr.refresh();
    // } else {
    //   Get.to(() => classs);
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: widget.lesson != null
            ? boxWidgetLesson(
                widget.lesson!,
              )
            : widget.quiz != null
                ? boxWidgetQuiz(
                    widget.quiz!,
                  )
                : boxWidgetShuffleQuiz(
                    widget.shuffleQuiz,
                  ),
      ),
    );
  }

  Column boxWidgetShuffleQuiz(
    ShuffleQuizModel? shuffleQuiz,
  ) {
    final ProfileController profileController = Get.find();

    bool isQuizCompleted = profileController
            .userr.value.completedLessonQuizesInThirtyDays
            ?.containsKey(shuffleQuiz!.quizId) ??
        false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            boxTileCategoryImageBuilder(shuffleQuiz!.category ?? ''),
            const SizedBox(width: 16.0),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  onTapp(
                      isCompleted: isQuizCompleted,
                      id: shuffleQuiz.quizId!,
                      classs: ShuffleQuiz(quiz: shuffleQuiz));
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Txt(
                      txt: shuffleQuiz.quizName ?? '',
                      maxLines: 5,
                      fontSize: 14,
                    ),
                    const Txt(
                      txt: 'Shuffle Kviz',
                      fontSize: 14,
                      maxLines: 5,
                      fontWeight: FontWeight.normal,
                      fontColor: grey2Color,
                    ),
                  ],
                ),
              ),
            ),
            profileController.userr.value.uid != null
                ? SavedShuffleQuizIconButton(
                    quiz: shuffleQuiz,
                  )
                : const SizedBox.shrink()
          ],
        ),
        const SizedBox(height: 16.0),
        GestureDetector(
          onTap: () {
            onTapp(
                isCompleted: isQuizCompleted,
                id: shuffleQuiz.quizId!,
                classs: ShuffleQuiz(quiz: shuffleQuiz));
          },
          child: MyImageCarousel(
            shuffleQuiz: shuffleQuiz,
          ),
        ),
        Row(
          children: [
            profileController.userr.value.uid != null
                ? FavShuffleQuizIconButton(
                    quiz: shuffleQuiz,
                  )
                : const SizedBox.shrink(),
            GestureDetector(
                onTap: () {
                  final service = ShareProductService();

                  service.shareProduct(shuffleQuiz.quizId!, 'shuffleQuiz');
                },
                child: SvgPicture.asset('assets/svgs/share_icon.svg')),
            const Spacer(),
            isQuizCompleted
                ? const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 24.0,
                  )
                : const SizedBox.shrink(),
          ],
        )
      ],
    );
  }

  Column boxWidgetQuiz(
    QuizModel quiz,
  ) {
    final ProfileController profileController = Get.find();
    bool isQuizCompleted = profileController
            .userr.value.completedLessonQuizesInThirtyDays
            ?.containsKey(quiz.quizId) ??
        false;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            boxTileCategoryImageBuilder(quiz.category ?? ''),
            const SizedBox(width: 16.0),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  onTapp(
                      id: quiz.quizId!,
                      classs: Quiz(quiz: quiz),
                      isCompleted: isQuizCompleted);
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Txt(
                      txt: quiz.quizName!,
                      maxLines: 5,
                      fontSize: 14,
                    ),
                    const Txt(
                      txt: 'Kviz',
                      fontSize: 14,
                      maxLines: 5,
                      fontWeight: FontWeight.normal,
                      fontColor: grey2Color,
                    ),
                  ],
                ),
              ),
            ),
            profileController.userr.value.uid != null
                ? SavedQuizIconButton(quiz: quiz)
                : const SizedBox.shrink()
          ],
        ),
        const SizedBox(height: 16.0),
        // Image.network(quiz.quizImageLink!),
        GestureDetector(
          onTap: () {
            onTapp(
                id: quiz.quizId!,
                classs: Quiz(quiz: quiz),
                isCompleted: isQuizCompleted);
          },
          child: MyImageCarousel(
            quiz: quiz,
          ),
        ),
        Row(
          children: [
            profileController.userr.value.uid != null
                ? FavQuizIconButton(
                    quiz: quiz,
                  )
                : const SizedBox.shrink(),
            GestureDetector(
                onTap: () {
                  final service = ShareProductService();

                  service.shareProduct(quiz.quizId!, 'quiz');
                },
                child: SvgPicture.asset('assets/svgs/share_icon.svg')),
            const Spacer(),
            isQuizCompleted
                ? const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 24.0,
                  )
                : const SizedBox.shrink(),
          ],
        )
      ],
    );
  }

  Column boxWidgetLesson(
    LessonModel lesson,
  ) {
    final ProfileController profileController = Get.find();
    bool isLessonCompleted = profileController
            .userr.value.completedLessonQuizesInThirtyDays
            ?.containsKey(lesson.lessonId) ??
        false;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            boxTileCategoryImageBuilder(lesson.category ?? ''),
            const SizedBox(width: 16.0),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  onTapp(
                      isCompleted: isLessonCompleted,
                      id: lesson.lessonId!,
                      classs: LessonIntro(lesson: lesson));
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Txt(
                      txt: lesson.lessonName ?? '',
                      maxLines: 5,
                      fontSize: 14,
                    ),
                    Txt(
                      txt: lesson.category ?? '',
                      maxLines: 5,
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      fontColor: grey2Color,
                    ),
                  ],
                ),
              ),
            ),
            profileController.userr.value.uid != null
                ? SavedLessonIconButton(
                    lesson: lesson,
                  )
                : const SizedBox.shrink()
          ],
        ),
        const SizedBox(height: 16.0),
        GestureDetector(
          onTap: () {
            onTapp(
                id: lesson.lessonId!,
                classs: LessonIntro(lesson: lesson),
                isCompleted: isLessonCompleted);
          },
          child: MyImageCarousel(
            lesson: lesson,
          ),
        ),
        Row(
          children: [
            profileController.userr.value.uid != null
                ? FavLessonIconButton(
                    lesson: lesson,
                  )
                : const SizedBox.shrink(),
            GestureDetector(
                onTap: () {
                  final service = ShareProductService();

                  service.shareProduct(lesson.lessonId!, 'lesson');
                },
                child: SvgPicture.asset('assets/svgs/share_icon.svg')),
            const Spacer(),
            isLessonCompleted
                ? const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 24.0,
                  )
                : const SizedBox.shrink(),
          ],
        )
      ],
    );
  }
}
