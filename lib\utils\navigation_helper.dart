import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NavigationHelper {
  // Standard transition durations for consistency
  static const Duration _standardDuration = Duration(milliseconds: 300);
  static const Duration _fastDuration = Duration(milliseconds: 200);
  static const Duration _slowDuration = Duration(milliseconds: 400);

  // Standard curves for professional feel
  static const Curve _standardCurve = Curves.easeInOut;
  static const Curve _fastCurve = Curves.easeOut;
  static const Curve _slowCurve = Curves.easeInOutCubic;

  // Smooth navigation to a new screen with optimized defaults
  static void navigateTo(
    Widget page, {
    Transition? transition,
    Duration? duration,
    Curve? curve,
    bool preventDuplicates = true,
  }) {
    if (preventDuplicates && Get.currentRoute == page.runtimeType.toString()) {
      return; // Prevent duplicate navigation
    }

    Get.to(
      () => page,
      transition: transition ?? Transition.cupertino,
      duration: duration ?? _standardDuration,
      curve: curve ?? _standardCurve,
      preventDuplicates: preventDuplicates,
    );
  }

  // Replace current screen with smooth transition
  static void navigateOff(
    Widget page, {
    Transition? transition,
    Duration? duration,
    Curve? curve,
  }) {
    Get.off(
      () => page,
      transition: transition ?? Transition.cupertino,
      duration: duration ?? _standardDuration,
      curve: curve ?? _standardCurve,
    );
  }

  // Replace all screens with smooth transition
  static void navigateOffAll(
    Widget page, {
    Transition? transition,
    Duration? duration,
    Curve? curve,
  }) {
    Get.offAll(
      () => page,
      transition: transition ?? Transition.fadeIn,
      duration: duration ?? _slowDuration,
      curve: curve ?? _slowCurve,
    );
  }

  // Navigate back with check
  static void navigateBack({
    dynamic result,
    bool closeOverlays = false,
  }) {
    if (Navigator.canPop(Get.context!)) {
      Get.back(result: result, closeOverlays: closeOverlays);
    }
  }

  // Navigate to home with proper stack clearing
  static void navigateToHome(Widget homePage) {
    Get.offUntil(
      GetPageRoute(
        page: () => homePage,
        transition: Transition.fadeIn,
        transitionDuration: _slowDuration,
        curve: _slowCurve,
      ),
      (route) => route.isFirst,
    );
  }

  // Smooth fade transition for auth flows
  static void navigateToAuth(Widget authPage) {
    Get.offAll(
      () => authPage,
      transition: Transition.fadeIn,
      duration: _slowDuration,
      curve: _slowCurve,
    );
  }

  // Quick navigation for internal app flows
  static void quickNavigateTo(Widget page) {
    Get.to(
      () => page,
      transition: Transition.cupertino,
      duration: _fastDuration,
      curve: _fastCurve,
    );
  }

  // Slide from bottom for modals/sheets
  static void slideFromBottom(Widget page) {
    Get.to(
      () => page,
      transition: Transition.downToUp,
      duration: _standardDuration,
      curve: _fastCurve,
    );
  }

  // Professional fade navigation for main flows
  static void fadeNavigateTo(Widget page) {
    Get.to(
      () => page,
      transition: Transition.fadeIn,
      duration: _standardDuration,
      curve: _standardCurve,
    );
  }

  // Fast slide navigation for quick actions
  static void slideNavigateTo(Widget page) {
    Get.to(
      () => page,
      transition: Transition.rightToLeft,
      duration: _fastDuration,
      curve: _fastCurve,
    );
  }
}
