import 'package:cloud_firestore/cloud_firestore.dart';

class UserModel {
  String? name;
  int? appOpenCount;
  Timestamp? lastPopupShownTimestamp;
  String? surname;
  String? uniqueName;

  String? email;
  String? profilePhoto;
  String? uid;
  int? neurons;
  int? lastMilestone;
  int? correctQuizAnswersCounter;
  Map<String, dynamic>? completedLessonQuizesInThirtyDays;
  int? weeklyStreak;
  int? consecutiveStreak;
  int? hearts;
  bool? isPremiumUser;
  bool? isNotificationOn;
  bool? isSoundOn;
  bool? hasSeenOnboadingScreen;
  bool? hasSeenInfoSheet;
  String? title;
  String? lastInactivityNotification;
  List<String>? achievements;
  List<String>? listOfFavCategories;
  List<String>? listOfLibraryCategories;
  Timestamp? lastActiveTime;
  Timestamp? lastStreakUpdate;
  String? deviceToken;
  Timestamp? lastRewardLessonMarathonerTimestamp;
  String? league;
  String? groupId;

  List<String>? completedQuizIds;
  List<String>? completedArticleIds;
  List<String>? openedArticleIds;

  int? totalTimeSpent;

  int? consecutivePerfectQuizzes;

  int? leagueWinsCount;
  int? leagueEventsCounter;

  Map<String, dynamic>? openedQuizesAndArticlesinMonth;
  List<String>? completedQuizesOfMythologyCategory;
  List<String>? completedQuizesOfCosmosCategory;
  List<String>? completedQuizesOfHistoryCategory;
  List<String>? completedQuizesOfScienceCategory;
  List<String>? completedQuizesOfArtCategory;

  // New fields for multi-category quizzes
  List<String>? listOfMultiCategoryQuizes;
  Timestamp? multiCategoryLastRewardedAt;
  Timestamp? lastListResetTimestamp;

  // New fields for league milestones
  bool? hasReachedBronzeLeague;
  bool? hasReachedSilverLeague;
  bool? hasReachedGoldLeague;

  // New field for 14-day streak count
  int? completedQuizCountDailyFourteenDayStreakCount;

  // Additional achievement tracking fields
  int? loyalUserStreakDays; // For 90-day streak tracking
  int? perfectQuizStreak; // For 5 consecutive perfect quizzes
  Timestamp? lastDailyQuizCompletion; // For consistent learner achievement

  // Weekly XP tracking fields
  int? weeklyScore;
  Timestamp? lastWeeklyReset;

  // Achievement timing tracking fields
  Timestamp? lastDailyLearnerReward; // For monthly Daily Learner achievement
  Timestamp? lastWeeklyStreakReward; // For weekly Weekly Streak achievement

  // Enhanced leaderboard tracking fields
  String?
      leagueGroup; // Group ID within the league (e.g., "group_1", "group_2")
  int? previousWeekScore; // Score from previous week for grouping logic
  int? leaguePromotionCount; // Total number of promotions
  int? leagueDemotionCount; // Total number of demotions
  Timestamp? lastLeagueChange; // When user last changed leagues
  Timestamp? lastScoreUpdate; // When score was last updated (for tie-breaking)
  List<String>? leagueHistory; // History of leagues user has been in
  int? weeksSinceLastActive; // Weeks since user was last active
  bool? isLeagueEligible; // Whether user is eligible for league participation
  int? currentLeagueWeek; // Current week number in league system

  UserModel({
    this.name,
    this.surname,
    this.uniqueName,
    this.groupId,
    this.appOpenCount = 0,
    this.lastPopupShownTimestamp,
    this.lastInactivityNotification,
    this.email,
    this.profilePhoto,
    this.hasSeenOnboadingScreen,
    this.hasSeenInfoSheet,
    this.lastRewardLessonMarathonerTimestamp,
    this.isPremiumUser,
    this.uid,
    this.neurons,
    this.weeklyStreak,
    this.consecutiveStreak,
    this.hearts,
    this.isNotificationOn,
    this.isSoundOn,
    this.title,
    this.achievements,
    this.listOfFavCategories,
    this.listOfLibraryCategories,
    this.lastActiveTime,
    this.lastStreakUpdate,
    this.deviceToken,
    this.openedArticleIds = const [],
    this.completedQuizIds = const [],
    this.completedArticleIds = const [],
    this.league,
    this.totalTimeSpent = 0,
    this.lastMilestone = 0,
    this.consecutivePerfectQuizzes = 0,
    this.correctQuizAnswersCounter = 0,
    this.leagueWinsCount = 0,
    this.leagueEventsCounter = 0,
    this.openedQuizesAndArticlesinMonth = const {},
    this.completedQuizesOfMythologyCategory = const [],
    this.completedQuizesOfCosmosCategory = const [],
    this.completedQuizesOfHistoryCategory = const [],
    this.completedQuizesOfScienceCategory = const [],
    this.completedQuizesOfArtCategory = const [],
    this.listOfMultiCategoryQuizes = const [],
    this.multiCategoryLastRewardedAt,
    this.lastListResetTimestamp,
    this.hasReachedBronzeLeague = false,
    this.hasReachedSilverLeague = false,
    this.hasReachedGoldLeague = false,
    this.completedQuizCountDailyFourteenDayStreakCount = 0,
    this.loyalUserStreakDays = 0,
    this.perfectQuizStreak = 0,
    this.lastDailyQuizCompletion,
    this.completedLessonQuizesInThirtyDays = const {},
    this.weeklyScore = 0,
    this.lastWeeklyReset,
    this.lastDailyLearnerReward,
    this.lastWeeklyStreakReward,
    this.leagueGroup,
    this.previousWeekScore = 0,
    this.leaguePromotionCount = 0,
    this.leagueDemotionCount = 0,
    this.lastLeagueChange,
    this.lastScoreUpdate,
    this.leagueHistory = const [],
    this.weeksSinceLastActive = 0,
    this.isLeagueEligible = true,
    this.currentLeagueWeek = 0,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      name: json['name'],
      groupId: json['groupId'],
      appOpenCount: json['appOpenCount'] ?? 0,
      lastPopupShownTimestamp: json['lastPopupShownTimestamp'],
      hasSeenOnboadingScreen: json['hasSeenOnboadingScreen'],
      hasSeenInfoSheet: json['hasSeenInfoSheet'],
      surname: json['surname'],
      uniqueName: json['uniqueName'],
      email: json['email'],
      profilePhoto: json['profilePhoto'],
      uid: json['uid'],
      lastRewardLessonMarathonerTimestamp:
          json['lastRewardLessonMarathonerTimestamp'],
      neurons: json['neurons'] ?? 0,
      weeklyStreak: json['weeklyStreak'] ?? 0,
      consecutiveStreak: json['consecutiveStreak'] ?? 0,
      hearts: json['hearts'] ?? 0,
      isPremiumUser: json['isPremiumUser'],
      isNotificationOn: json['isNotificationOn'],
      isSoundOn: json['isSoundOn'],
      title: json['title'],
      achievements: List<String>.from(json['achievements'] ?? []),
      listOfFavCategories: List<String>.from(json['listOfFavCategories'] ?? []),
      listOfLibraryCategories:
          List<String>.from(json['listOfLibraryCategories'] ?? []),
      lastActiveTime: json['lastActiveTime'] ?? Timestamp.now(),
      lastStreakUpdate: json['lastStreakUpdate'],
      deviceToken: json['deviceToken'],
      completedQuizIds: List<String>.from(json['completedQuizIds'] ?? []),
      openedArticleIds: List<String>.from(json['openedArticleIds'] ?? []),
      completedArticleIds: List<String>.from(json['completedArticleIds'] ?? []),
      league: json['league'],
      totalTimeSpent: json['totalTimeSpent'] ?? 0,
      lastMilestone: json['lastMilestone'] ?? 0,
      consecutivePerfectQuizzes: json['consecutivePerfectQuizzes'] ?? 0,
      correctQuizAnswersCounter: json['correctQuizAnswersCounter'] ?? 0,
      leagueWinsCount: json['leagueWinsCount'] ?? 0,
      leagueEventsCounter: json['leagueEventsCounter'] ?? 0,
      completedQuizesOfMythologyCategory:
          List<String>.from(json['completedQuizesOfMythologyCategory'] ?? []),
      openedQuizesAndArticlesinMonth:
          json['openedQuizesAndArticlesinMonth'] ?? {},
      completedQuizesOfCosmosCategory:
          List<String>.from(json['completedQuizesOfCosmosCategory'] ?? []),
      completedQuizesOfHistoryCategory:
          List<String>.from(json['completedQuizesOfHistoryCategory'] ?? []),
      completedQuizesOfScienceCategory:
          List<String>.from(json['completedQuizesOfScienceCategory'] ?? []),
      completedQuizesOfArtCategory:
          List<String>.from(json['completedQuizesOfArtCategory'] ?? []),
      listOfMultiCategoryQuizes:
          List<String>.from(json['listOfMultiCategoryQuizes'] ?? []),
      multiCategoryLastRewardedAt: json['multiCategoryLastRewardedAt'],
      lastListResetTimestamp: json['lastListResetTimestamp'],
      hasReachedBronzeLeague: json['hasReachedBronzeLeague'] ?? false,
      hasReachedSilverLeague: json['hasReachedSilverLeague'] ?? false,
      hasReachedGoldLeague: json['hasReachedGoldLeague'] ?? false,
      completedQuizCountDailyFourteenDayStreakCount:
          json['completedQuizCountDailyFourteenDayStreakCount'] ?? 0,
      loyalUserStreakDays: json['loyalUserStreakDays'] ?? 0,
      perfectQuizStreak: json['perfectQuizStreak'] ?? 0,
      lastDailyQuizCompletion: json['lastDailyQuizCompletion'],
      completedLessonQuizesInThirtyDays:
          json['completedLessonQuizesInThirtyDays'] ?? {},
      weeklyScore: json['weeklyScore'] ?? 0,
      lastWeeklyReset: json['lastWeeklyReset'],
      lastDailyLearnerReward: json['lastDailyLearnerReward'],
      lastWeeklyStreakReward: json['lastWeeklyStreakReward'],
      leagueGroup: json['leagueGroup'],
      previousWeekScore: json['previousWeekScore'] ?? 0,
      leaguePromotionCount: json['leaguePromotionCount'] ?? 0,
      leagueDemotionCount: json['leagueDemotionCount'] ?? 0,
      lastLeagueChange: json['lastLeagueChange'],
      lastScoreUpdate: json['lastScoreUpdate'],
      leagueHistory: List<String>.from(json['leagueHistory'] ?? []),
      weeksSinceLastActive: json['weeksSinceLastActive'] ?? 0,
      isLeagueEligible: json['isLeagueEligible'] ?? true,
      currentLeagueWeek: json['currentLeagueWeek'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
        "name": name,
        "groupId": groupId,
        "surname": surname,
        "uniqueName": uniqueName,
        "appOpenCount": appOpenCount,
        "lastPopupShownTimestamp": lastPopupShownTimestamp,
        "hasSeenOnboadingScreen": hasSeenOnboadingScreen,
        "hasSeenInfoSheet": hasSeenInfoSheet,
        "email": email,
        "uid": uid,
        "lastRewardLessonMarathonerTimestamp":
            lastRewardLessonMarathonerTimestamp,
        "profilePhoto": profilePhoto,
        "neurons": neurons,
        "weeklyStreak": weeklyStreak,
        "consecutiveStreak": consecutiveStreak,
        "hearts": hearts,
        "isPremiumUser": isPremiumUser,
        "isNotificationOn": isNotificationOn,
        "isSoundOn": isSoundOn,
        "title": title,
        "achievements": achievements,
        "listOfFavCategories": listOfFavCategories,
        "listOfLibraryCategories": listOfLibraryCategories,
        "lastActiveTime": lastActiveTime,
        "lastStreakUpdate": lastStreakUpdate,
        "deviceToken": deviceToken,
        "completedQuizIds": completedQuizIds,
        "openedArticleIds": openedArticleIds,
        "completedArticleIds": completedArticleIds,
        "league": league,
        "totalTimeSpent": totalTimeSpent,
        "lastMilestone": lastMilestone,
        "consecutivePerfectQuizzes": consecutivePerfectQuizzes,
        "correctQuizAnswersCounter": correctQuizAnswersCounter,
        "leagueWinsCount": leagueWinsCount,
        "leagueEventsCounter": leagueEventsCounter,
        "completedQuizesOfMythologyCategory":
            completedQuizesOfMythologyCategory,
        "openedQuizesAndArticlesinMonth": openedQuizesAndArticlesinMonth,
        "completedQuizesOfCosmosCategory": completedQuizesOfCosmosCategory,
        "completedQuizesOfHistoryCategory": completedQuizesOfHistoryCategory,
        "completedQuizesOfScienceCategory": completedQuizesOfScienceCategory,
        "completedQuizesOfArtCategory": completedQuizesOfArtCategory,
        "listOfMultiCategoryQuizes": listOfMultiCategoryQuizes,
        "multiCategoryLastRewardedAt": multiCategoryLastRewardedAt,
        "lastListResetTimestamp": lastListResetTimestamp,
        "hasReachedBronzeLeague": hasReachedBronzeLeague,
        "hasReachedSilverLeague": hasReachedSilverLeague,
        "hasReachedGoldLeague": hasReachedGoldLeague,
        "completedQuizCountDailyFourteenDayStreakCount":
            completedQuizCountDailyFourteenDayStreakCount,
        "loyalUserStreakDays": loyalUserStreakDays,
        "perfectQuizStreak": perfectQuizStreak,
        "lastDailyQuizCompletion": lastDailyQuizCompletion,
        "completedLessonQuizesInThirtyDays": completedLessonQuizesInThirtyDays,
        "weeklyScore": weeklyScore,
        "lastWeeklyReset": lastWeeklyReset,
        "lastDailyLearnerReward": lastDailyLearnerReward,
        "lastWeeklyStreakReward": lastWeeklyStreakReward,
        "leagueGroup": leagueGroup,
        "previousWeekScore": previousWeekScore,
        "leaguePromotionCount": leaguePromotionCount,
        "leagueDemotionCount": leagueDemotionCount,
        "lastLeagueChange": lastLeagueChange,
        "lastScoreUpdate": lastScoreUpdate,
        "leagueHistory": leagueHistory,
        "weeksSinceLastActive": weeksSinceLastActive,
        "isLeagueEligible": isLeagueEligible,
        "currentLeagueWeek": currentLeagueWeek,
      };

  static UserModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return UserModel.fromJson(snapshot);
  }
}
