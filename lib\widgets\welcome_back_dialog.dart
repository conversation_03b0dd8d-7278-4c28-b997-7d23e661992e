import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:bibl/widgets/dialog_widget.dart';
import 'package:flutter/material.dart';

class WelcomeBackDialog extends StatelessWidget {
  const WelcomeBackDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return dialogWidget(
      Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 10),
          const Txt(txt: 'Welcome Back', fontSize: 22),
          const SizedBox(height: 10),
          const Txt(
            txt:
                'You have unlocked a new achievement! 🎉 Keep up the great work!',
            fontSize: 16,
            maxLines: 3,
            fontColor: Colors.grey,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Container(
            decoration: BoxDecoration(
                color: mainColor.withValues(alpha: 0.2),
                border: Border.all(color: mainColor),
                borderRadius: BorderRadius.circular(10)),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  Row(
                    children: [
                      const CircleAvatar(
                        radius: 30,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          rowWidget('Rank', '5'),
                          rowWidget('Titula', 'Trivia definitiva'),
                          rowWidget('Neuroni', '450'),
                        ],
                      )
                    ],
                  ),
                  const Txt(
                    txt:
                        'Great job on your achievements so far! Keep trying your best to score well in the quiz!',
                    fontSize: 16,
                    maxLines: 3,
                    fontColor: Colors.grey,
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          buttonContainer(text: 'Explore More Lessons')
        ],
      ),
    );
  }

  Row rowWidget(String title, String value) {
    return Row(
      children: [
        Txt(
          txt: title,
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
        const SizedBox(
          width: 10,
        ),
        Txt(
          txt: value,
          fontSize: 16,
          fontColor: Colors.grey,
        )
      ],
    );
  }
}
