import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import 'dialog_widget.dart';

class AchievementDialog extends StatelessWidget {
  final String image;
  final String message;

  const AchievementDialog(
      {super.key, required this.image, required this.message});

  @override
  Widget build(BuildContext context) {
    return dialogWidget(
      Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 10),
          const Txt(
            txt: 'Čestitamo!',
            fontWeight: FontWeight.w600,
            font: 'Inter',
            fontSize: 20,
          ),
          const SizedBox(height: 30),
          SvgPicture.asset(
            'assets/svgs/achievementsImages/$image.svg',
            height: 80,
          ),
          const SizedBox(height: 30),
          const Txt(
            txt: 'Otključali ste novo dostignuće!🎉 Samo tako nastavite!',
            fontSize: 16,
            maxLines: 3,
            fontColor: grey2Color,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Txt(
            txt: message,
            fontSize: 14,
            fontWeight: FontWeight.normal,
            maxLines: 3,
            fontColor: grey2Color,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          buttonContainer(
            text: 'Nastavi',
            onTap: () => Get.back(),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}
