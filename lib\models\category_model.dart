import 'package:cloud_firestore/cloud_firestore.dart';

class CategoryModel {
  String? topicName;

  String? topicPhotoLink;

  CategoryModel({
    this.topicName,
    this.topicPhotoLink,
  });

  static CategoryModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return CategoryModel(
      topicName: snapshot['topicName'],
      topicPhotoLink: snapshot['topicPhotoLink'],
    );
  }
}
