import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Simplified leaderboard service - 10 users max per league, no groups
class LeaderboardService {
  static const List<String> _leagues = [
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>etist',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    'Korals<PERSON>',
    'Jupiter',
    'Elit<PERSON>'
  ];

  static const int _maxPlayersPerLeague = 10;
  static const String _defaultLeague = 'Bronzana';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _userId;

  LeaderboardService(this._userId);

  /// Adds a user to the appropriate league with perfect grouping
  Future<void> addUserToLeague({
    required String username,
    String? targetLeague,
  }) async {
    try {
      final league = targetLeague ?? _defaultLeague;
      final groupId = await _findOrCreateAvailableGroup(league);
      await _addUserToLeagueGroup(league, groupId, username);
      await _updateUserLeagueInfo(league, groupId);
    } catch (e) {
      _logError('Failed to add user to league', e);
      rethrow;
    }
  }

  /// Find or create an available group in a league (simplified for small user base)
  Future<String> _findOrCreateAvailableGroup(String league) async {
    // For small user bases, use a single group per league
    const defaultGroupId = 'group_1';

    // Check if the default group exists
    final groupDoc = await _firestore
        .collection('leaderboards')
        .doc(league)
        .collection('groups')
        .doc(defaultGroupId)
        .get();

    if (!groupDoc.exists) {
      // Create the default group
      await _firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .doc(defaultGroupId)
          .set({
        'createdAt': FieldValue.serverTimestamp(),
        'league': league,
        'groupId': defaultGroupId,
      });
    }

    return defaultGroupId;
  }

  /// Migrate users from old structure to new group structure
  Future<void> migrateUsersToGroups() async {
    try {
      // Get all users
      final usersSnapshot = await _firestore.collection('users').get();

      for (final userDoc in usersSnapshot.docs) {
        final userData = userDoc.data();
        final league = userData['league'] as String? ?? _defaultLeague;
        final leagueGroup = userData['leagueGroup'] as String?;

        // If user doesn't have a group assigned, assign them to group_1
        if (leagueGroup == null) {
          await _updateUserLeagueInfo(league, 'group_1');

          // Also add them to the leaderboard group structure
          await _addUserToLeagueGroup(
              league, 'group_1', userData['uniqueName'] ?? 'Unknown');
        }
      }
    } catch (e) {
      _logError('Failed to migrate users to groups', e);
    }
  }

  /// Add user to a specific league group
  Future<void> _addUserToLeagueGroup(
      String league, String groupId, String username) async {
    // Ensure we have a valid username
    String finalUsername = username;
    if (username.isEmpty || username == 'Unknown') {
      // Get user's name from profile and generate unique username
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        final name = userData['name'] as String? ?? 'User';
        finalUsername = await _generateUniqueUsername(name);

        // Update user's profile with the generated username
        await _firestore.collection('users').doc(_userId).update({
          'uniqueName': finalUsername,
        });
      } else {
        finalUsername = '@user-${_userId.substring(0, 6)}';
      }
    }

    // Add user to the specific group within the league
    await _firestore
        .collection('leaderboards')
        .doc(league)
        .collection('groups')
        .doc(groupId)
        .collection('players')
        .doc(_userId)
        .set({
      'username': finalUsername,
      'league': league,
      'score': 0,
      'playerId': _userId,
      'lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  /// Update user's league information in their profile
  Future<void> _updateUserLeagueInfo(String league, String groupId) async {
    await _firestore.collection('users').doc(_userId).update({
      'league': league,
      'leagueGroup': groupId,
      'lastLeagueUpdate': FieldValue.serverTimestamp(),
      'isLeagueEligible': true,
    });
  }

  /// Update user's weekly score
  Future<void> updateWeeklyScore(int scoreToAdd) async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      if (!userDoc.exists) return;

      final userData = userDoc.data()!;
      final currentLeague = userData['league'] as String? ?? _defaultLeague;
      final currentGroup = userData['leagueGroup'] as String?;

      if (currentGroup == null) {
        _logError('User has no group assigned', 'Missing leagueGroup field');
        return;
      }

      // Update score in leaderboard group
      await _firestore
          .collection('leaderboards')
          .doc(currentLeague)
          .collection('groups')
          .doc(currentGroup)
          .collection('players')
          .doc(_userId)
          .update({
        'score': FieldValue.increment(scoreToAdd),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      // Update weekly score and tracking in user profile
      await _firestore.collection('users').doc(_userId).update({
        'weeklyScore': FieldValue.increment(scoreToAdd),
        'lastScoreUpdate': FieldValue.serverTimestamp(),
        'weeksSinceLastActive': 0,
      });
    } catch (e) {
      _logError('Failed to update weekly score', e);
    }
  }

  /// Get current league standings for user's group
  Future<List<Map<String, dynamic>>> getLeagueStandings(String league) async {
    try {
      // First get the current user's group
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      if (!userDoc.exists) return [];

      final userData = userDoc.data()!;
      final userGroup = userData['leagueGroup'] as String?;

      if (userGroup == null) {
        _logError('User has no group assigned', 'Missing leagueGroup field');
        return [];
      }

      // Get standings for the user's specific group
      final playersSnapshot = await _firestore
          .collection('leaderboards')
          .doc(league)
          .collection('groups')
          .doc(userGroup)
          .collection('players')
          .orderBy('score', descending: true)
          .orderBy('lastUpdated',
              descending: false) // Tie-breaker: earlier achiever wins
          .limit(_maxPlayersPerLeague)
          .get();

      return playersSnapshot.docs
          .map((doc) => {
                'playerId': doc.id,
                'username': doc.data()['username'] ?? 'Unknown',
                'score': doc.data()['score'] ?? 0,
                'league': doc.data()['league'] ?? league,
                'groupId': userGroup,
              })
          .toList();
    } catch (e) {
      _logError('Failed to get league standings', e);
      return [];
    }
  }

  /// Get user's current position in their league
  Future<int> getUserPosition() async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      if (!userDoc.exists) return -1;

      final currentLeague =
          userDoc.data()!['league'] as String? ?? _defaultLeague;
      final standings = await getLeagueStandings(currentLeague);

      for (int i = 0; i < standings.length; i++) {
        if (standings[i]['playerId'] == _userId) {
          return i + 1; // 1-based position
        }
      }

      return -1; // Not found
    } catch (e) {
      _logError('Failed to get user position', e);
      return -1;
    }
  }

  /// Static helper methods
  static List<String> get leagues => List.unmodifiable(_leagues);
  static String get defaultLeague => _defaultLeague;
  static int get maxPlayersPerLeague => _maxPlayersPerLeague;

  /// Gets league index (0-based)
  static int getLeagueIndex(String league) {
    return _leagues.indexOf(league);
  }

  /// Gets next league for promotion
  static String? getNextLeague(String currentLeague) {
    final index = getLeagueIndex(currentLeague);
    if (index == -1 || index >= _leagues.length - 1) return null;
    return _leagues[index + 1];
  }

  /// Gets previous league for demotion
  static String? getPreviousLeague(String currentLeague) {
    final index = getLeagueIndex(currentLeague);
    if (index <= 0) return null;
    return _leagues[index - 1];
  }

  /// Generate unique username for a user
  Future<String> _generateUniqueUsername(String name) async {
    final baseName = name.toLowerCase().replaceAll(' ', '');
    int suffix = 1;
    String uniqueUsername;

    while (true) {
      uniqueUsername = '@$baseName-$suffix';

      final existing = await _firestore
          .collection('users')
          .where('uniqueName', isEqualTo: uniqueUsername)
          .get();

      if (existing.docs.isEmpty) {
        break;
      }

      suffix++;
    }

    return uniqueUsername;
  }

  /// Utility methods for logging
  void _logError(String message, dynamic error) {
    if (kDebugMode) {
      print('LeaderboardService Error: $message - $error');
    }
  }
}
