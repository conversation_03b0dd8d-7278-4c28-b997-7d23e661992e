import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Simplified leaderboard service - 10 users max per league, no groups
class LeaderboardService {
  static const List<String> _leagues = [
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>etist',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    'Korals<PERSON>',
    '<PERSON>',
    'Elit<PERSON>'
  ];

  static const int _maxPlayersPerLeague = 10;
  static const String _defaultLeague = 'Bronzana';

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String _userId;

  LeaderboardService(this._userId);

  /// Adds a user to the appropriate league (simplified - no groups)
  Future<void> addUserToLeague({
    required String username,
    String? targetLeague,
  }) async {
    try {
      final league = await _findAvailableLeague(targetLeague);
      await _addUserToLeague(league, username);
      await _updateUserLeagueInfo(league);
    } catch (e) {
      _logError('Failed to add user to league', e);
      rethrow;
    }
  }

  /// Find a league with available spots (less than 10 players)
  Future<String> _findAvailableLeague(String? targetLeague) async {
    final startLeague = targetLeague ?? _defaultLeague;

    // Check if target league has space
    if (await _hasAvailableSpots(startLeague)) {
      return startLeague;
    }

    // If target league is full, find any available league starting from Bronze
    for (final league in _leagues) {
      if (await _hasAvailableSpots(league)) {
        return league;
      }
    }

    // If all leagues are full, create new instance of Bronze league
    return _defaultLeague;
  }

  /// Check if a league has available spots (less than 10 players)
  Future<bool> _hasAvailableSpots(String league) async {
    final playersSnapshot = await _firestore
        .collection('leaderboards')
        .doc(league)
        .collection('players')
        .get();

    return playersSnapshot.docs.length < _maxPlayersPerLeague;
  }

  /// Add user to a specific league
  Future<void> _addUserToLeague(String league, String username) async {
    // Ensure we have a valid username
    String finalUsername = username;
    if (username.isEmpty || username == 'Unknown') {
      // Get user's name from profile and generate unique username
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      if (userDoc.exists) {
        final userData = userDoc.data()!;
        final name = userData['name'] as String? ?? 'User';
        finalUsername = await _generateUniqueUsername(name);

        // Update user's profile with the generated username
        await _firestore.collection('users').doc(_userId).update({
          'uniqueName': finalUsername,
        });
      } else {
        finalUsername = '@user-${_userId.substring(0, 6)}';
      }
    }

    await _firestore
        .collection('leaderboards')
        .doc(league)
        .collection('players')
        .doc(_userId)
        .set({
      'username': finalUsername,
      'league': league,
      'score': 0,
      'playerId': _userId,
      'lastUpdated': FieldValue.serverTimestamp(),
    });
  }

  /// Update user's league information in their profile
  Future<void> _updateUserLeagueInfo(String league) async {
    await _firestore.collection('users').doc(_userId).update({
      'league': league,
      'lastLeagueUpdate': FieldValue.serverTimestamp(),
    });
  }

  /// Update user's weekly score
  Future<void> updateWeeklyScore(int scoreToAdd) async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      if (!userDoc.exists) return;

      final userData = userDoc.data()!;
      final currentLeague = userData['league'] as String? ?? _defaultLeague;

      // Update score in leaderboard
      await _firestore
          .collection('leaderboards')
          .doc(currentLeague)
          .collection('players')
          .doc(_userId)
          .update({
        'score': FieldValue.increment(scoreToAdd),
        'lastUpdated': FieldValue.serverTimestamp(),
      });

      // Update weekly score in user profile
      await _firestore.collection('users').doc(_userId).update({
        'weeklyScore': FieldValue.increment(scoreToAdd),
      });
    } catch (e) {
      _logError('Failed to update weekly score', e);
    }
  }

  /// Get current league standings
  Future<List<Map<String, dynamic>>> getLeagueStandings(String league) async {
    try {
      final playersSnapshot = await _firestore
          .collection('leaderboards')
          .doc(league)
          .collection('players')
          .orderBy('score', descending: true)
          .limit(_maxPlayersPerLeague)
          .get();

      return playersSnapshot.docs
          .map((doc) => {
                'playerId': doc.id,
                'username': doc.data()['username'] ?? 'Unknown',
                'score': doc.data()['score'] ?? 0,
                'league': doc.data()['league'] ?? league,
              })
          .toList();
    } catch (e) {
      _logError('Failed to get league standings', e);
      return [];
    }
  }

  /// Get user's current position in their league
  Future<int> getUserPosition() async {
    try {
      final userDoc = await _firestore.collection('users').doc(_userId).get();
      if (!userDoc.exists) return -1;

      final currentLeague =
          userDoc.data()!['league'] as String? ?? _defaultLeague;
      final standings = await getLeagueStandings(currentLeague);

      for (int i = 0; i < standings.length; i++) {
        if (standings[i]['playerId'] == _userId) {
          return i + 1; // 1-based position
        }
      }

      return -1; // Not found
    } catch (e) {
      _logError('Failed to get user position', e);
      return -1;
    }
  }

  /// Static helper methods
  static List<String> get leagues => List.unmodifiable(_leagues);
  static String get defaultLeague => _defaultLeague;
  static int get maxPlayersPerLeague => _maxPlayersPerLeague;

  /// Gets league index (0-based)
  static int getLeagueIndex(String league) {
    return _leagues.indexOf(league);
  }

  /// Gets next league for promotion
  static String? getNextLeague(String currentLeague) {
    final index = getLeagueIndex(currentLeague);
    if (index == -1 || index >= _leagues.length - 1) return null;
    return _leagues[index + 1];
  }

  /// Gets previous league for demotion
  static String? getPreviousLeague(String currentLeague) {
    final index = getLeagueIndex(currentLeague);
    if (index <= 0) return null;
    return _leagues[index - 1];
  }

  /// Generate unique username for a user
  Future<String> _generateUniqueUsername(String name) async {
    final baseName = name.toLowerCase().replaceAll(' ', '');
    int suffix = 1;
    String uniqueUsername;

    while (true) {
      uniqueUsername = '@$baseName-$suffix';

      final existing = await _firestore
          .collection('users')
          .where('uniqueName', isEqualTo: uniqueUsername)
          .get();

      if (existing.docs.isEmpty) {
        break;
      }

      suffix++;
    }

    return uniqueUsername;
  }

  /// Utility methods for logging
  void _logError(String message, dynamic error) {
    if (kDebugMode) {
      print('LeaderboardService Error: $message - $error');
    }
  }
}
