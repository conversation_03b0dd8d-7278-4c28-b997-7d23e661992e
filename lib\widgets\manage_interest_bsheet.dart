import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/controllers/category_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/category_icon_widget.dart';
import 'package:bibl/widgets/cross_widget.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ManageInterestsBottomSheet extends StatefulWidget {
  final bool isFromLibrary;
  const ManageInterestsBottomSheet({super.key, required this.isFromLibrary});

  @override
  ManageInterestsBottomSheetState createState() =>
      ManageInterestsBottomSheetState();
}

class ManageInterestsBottomSheetState
    extends State<ManageInterestsBottomSheet> {
  final CategoryController categoryController = Get.find();
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();
  List<String> selectedCategories = [];
  List<String> allCategories = [];

  @override
  void initState() {
    super.initState();
    allCategories = categoryController.allCategories
        .map((element) => element.topicName!)
        .toList();
    if (widget.isFromLibrary) {
      selectedCategories
          .addAll(profileController.userr.value.listOfLibraryCategories ?? []);
    } else {
      selectedCategories
          .addAll(profileController.userr.value.listOfFavCategories ?? []);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Stack(
        children: [
          // Scrollable content
          SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    IconButton(onPressed: null, icon: Container()),
                    const Spacer(),
                    Container(
                      height: 4,
                      width: Get.width * 0.4,
                      decoration: BoxDecoration(
                        color: grey2Color,
                        borderRadius: BorderRadius.circular(50),
                      ),
                    ),
                    const Spacer(),
                    crossWidget(),
                  ],
                ),
                const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Txt(
                      txt: 'Izaberi interesovanja',
                      fontSize: 20,
                    ),
                  ],
                ),
                const Padding(
                  padding: EdgeInsets.only(top: 16, bottom: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Flexible(
                        child: Txt(
                          txt:
                              'Možete ih prilagoditi kako biste dobili željeni sadržaj',
                          fontSize: 14,
                          maxLines: 5,
                          fontWeight: FontWeight.normal,
                          fontColor: grey2Color,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
                listOfCategoriesWithCustomIcon(),
                // Add padding to ensure content isn't obscured by the button
                const SizedBox(height: 80), // Space for the button
              ],
            ),
          ),
          // Fixed "Potvrdi" button at the bottom center with transparent background
          Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: buttonContainer(
                text: 'Potvrdi',
                onTap: () {
                  if (selectedCategories.isNotEmpty) {
                    Get.back();
                    if (widget.isFromLibrary) {
                      debugPrint(
                          'Updating library categories to: $selectedCategories');
                      profileController.userr.value.listOfLibraryCategories!
                          .clear();
                      profileController.userr.value.listOfLibraryCategories!
                          .addAll(selectedCategories);

                      // Trigger reactive update for nested property changes
                      profileController.userr.refresh();
                      debugPrint(
                          'Library categories after update: ${profileController.userr.value.listOfLibraryCategories}');

                      // Clear image cache to ensure fresh images for new categories
                      lessonController.clearImageCache();

                      // Clear displayed items immediately to prevent showing old content
                      lessonController.libraryDisplayedItems.clear();
                      lessonController.currentPageForLibraryItems.value = 1;

                      // Add small delay to ensure reactive update propagates
                      Future.delayed(const Duration(milliseconds: 200), () {
                        lessonController.shuffleAllItems(
                            from: 'sheet', isShuffle: true, shouldClear: true);
                      });

                      categoryController.addSelectedLibraryCategoriesToDatabase(
                          selectedCategories);
                    } else {
                      debugPrint(
                          'Updating home categories to: $selectedCategories');
                      profileController.userr.value.listOfFavCategories!
                          .clear();
                      profileController.userr.value.listOfFavCategories!
                          .addAll(selectedCategories);

                      // Trigger reactive update for nested property changes
                      profileController.userr.refresh();
                      debugPrint(
                          'Home categories after update: ${profileController.userr.value.listOfFavCategories}');

                      // Clear image cache to ensure fresh images for new categories
                      lessonController.clearImageCache();

                      // Clear displayed items immediately to prevent showing old content
                      lessonController.homeDisplayedItems.clear();
                      lessonController.currentPageForHomeItems.value = 1;

                      // Add small delay to ensure reactive update propagates
                      Future.delayed(const Duration(milliseconds: 200), () {
                        lessonController.mergeAndShuffleItems(
                            from: 'sheet', isShuffle: true, shouldClear: true);
                      });

                      categoryController
                          .addSelectedCategoriesToDatabase(selectedCategories);
                    }
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Dynamic List of Categories with Updated Logic
  ListView listOfCategoriesWithCustomIcon() {
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      separatorBuilder: (context, index) {
        return const SizedBox(
          height: 10,
        );
      },
      itemCount: categoryController.allCategories.length,
      itemBuilder: (context, index) {
        final category = categoryController.allCategories[index];
        final isSelected =
            selectedCategories.contains(category.topicName ?? '');
        return GestureDetector(
          onTap: () {
            setState(() {
              if (isSelected) {
                selectedCategories.remove(category.topicName);
              } else {
                if (widget.isFromLibrary || selectedCategories.length < 5) {
                  selectedCategories.add(category.topicName!);
                }
              }
            });
          },
          child: Container(
            height: 58,
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xffF8EFFF) : Colors.transparent,
              border: Border.all(
                color: isSelected
                    ? const Color(0xff9610FF).withValues(alpha: 0.5)
                    : Colors.black,
              ),
              borderRadius: BorderRadius.circular(50),
            ),
            child: ListTile(
              leading: SizedBox(
                width: Get.width * 0.1,
                child: CustomCategoryIcon(
                  topicName: category.topicName ?? '',
                  topicPhotoLink: category.topicPhotoLink ?? '',
                  isSelected: isSelected,
                ),
              ),
              title: Txt(
                txt: category.topicName ?? '',
                maxLines: 5,
                fontSize: 14,
                fontWeight: FontWeight.normal,
              ),
              trailing: Container(
                height: 20,
                width: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: isSelected
                      ? mainColorsGradient
                      : const LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [Colors.grey, Colors.grey],
                        ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(6.0),
                  child: Container(
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
