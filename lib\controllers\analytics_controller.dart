import '../res/style.dart';

class AnalticsController {
  screenViewAnalyticsUpdate(String screenName) async {
    await firebaseAnalytic
        .logEvent(name: 'Screen_view', parameters: {'screen_name': screenName});
  }

//lessons
  lessonOpenedAnalyticsUpdate(String lessonName) async {
    await firebaseAnalytic.logEvent(
        name: 'Lesson_Opened', parameters: {'lesson_name': lessonName});
  }

  lessonStartedAnalyticsUpdate(String lessonName) async {
    await firebaseAnalytic.logEvent(
        name: 'Lesson_Started', parameters: {'lesson_name': lessonName});
  }

//quizes
  quizOpenedAnalyticsUpdate(String quizName) async {
    await firebaseAnalytic
        .logEvent(name: 'Quiz_Opened', parameters: {'quiz_name': quizName});
  }

  quizStartedAnalyticsUpdate(String quizName) async {
    await firebaseAnalytic
        .logEvent(name: 'Quiz_Started', parameters: {'quiz_name': quizName});
  }

  subPlanMonthlyAnalyticsUpdate() async {
    await firebaseAnalytic.logEvent(
      name: 'SubPlan_Month',
    );
  }

  subPlanYearlyAnalyticsUpdate() async {
    await firebaseAnalytic.logEvent(
      name: 'SubPlan_Year',
    );
  }

  subPageDisplayedAnalyticsUpdate(String screenName) async {
    await firebaseAnalytic.logEvent(
        name: 'SubPage_Displayed', parameters: {'sub_screen': screenName});
  }
  // reviewButtonClick(String review) async {
  //   await firebaseAnalytic.logEvent(
  //       name: 'ReviewButtonClicked', parameters: {'review_clicked': review});
  // }
}
