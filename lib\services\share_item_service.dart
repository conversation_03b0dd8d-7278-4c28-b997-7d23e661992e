import 'package:share_plus/share_plus.dart';

class ShareProductService {
  String baseUrl = 'https://umnilab-link.web.app/';

  String generateLink(String id, String type) {
    return '$baseUrl?$type=$id';
  }

  Future<void> shareLink(String link) async {
    await Share.share(link);
  }

  void shareProduct(String id, String type) {
    final productLink = generateLink(id, type);
    shareLink(productLink);
  }
}
