PODS:
  - abseil/algorithm (1.20240116.1):
    - abseil/algorithm/algorithm (= 1.20240116.1)
    - abseil/algorithm/container (= 1.20240116.1)
  - abseil/algorithm/algorithm (1.20240116.1):
    - abseil/base/config
  - abseil/algorithm/container (1.20240116.1):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/meta/type_traits
  - abseil/base (1.20240116.1):
    - abseil/base/atomic_hook (= 1.20240116.1)
    - abseil/base/base (= 1.20240116.1)
    - abseil/base/base_internal (= 1.20240116.1)
    - abseil/base/config (= 1.20240116.1)
    - abseil/base/core_headers (= 1.20240116.1)
    - abseil/base/cycleclock_internal (= 1.20240116.1)
    - abseil/base/dynamic_annotations (= 1.20240116.1)
    - abseil/base/endian (= 1.20240116.1)
    - abseil/base/errno_saver (= 1.20240116.1)
    - abseil/base/fast_type_id (= 1.20240116.1)
    - abseil/base/log_severity (= 1.20240116.1)
    - abseil/base/malloc_internal (= 1.20240116.1)
    - abseil/base/no_destructor (= 1.20240116.1)
    - abseil/base/nullability (= 1.20240116.1)
    - abseil/base/prefetch (= 1.20240116.1)
    - abseil/base/pretty_function (= 1.20240116.1)
    - abseil/base/raw_logging_internal (= 1.20240116.1)
    - abseil/base/spinlock_wait (= 1.20240116.1)
    - abseil/base/strerror (= 1.20240116.1)
    - abseil/base/throw_delegate (= 1.20240116.1)
  - abseil/base/atomic_hook (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/base (1.20240116.1):
    - abseil/base/atomic_hook
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/cycleclock_internal
    - abseil/base/dynamic_annotations
    - abseil/base/log_severity
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/spinlock_wait
    - abseil/meta/type_traits
  - abseil/base/base_internal (1.20240116.1):
    - abseil/base/config
    - abseil/meta/type_traits
  - abseil/base/config (1.20240116.1)
  - abseil/base/core_headers (1.20240116.1):
    - abseil/base/config
  - abseil/base/cycleclock_internal (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/config
  - abseil/base/dynamic_annotations (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/endian (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
  - abseil/base/errno_saver (1.20240116.1):
    - abseil/base/config
  - abseil/base/fast_type_id (1.20240116.1):
    - abseil/base/config
  - abseil/base/log_severity (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/malloc_internal (1.20240116.1):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
  - abseil/base/no_destructor (1.20240116.1):
    - abseil/base/config
  - abseil/base/nullability (1.20240116.1):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/base/prefetch (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/base/pretty_function (1.20240116.1)
  - abseil/base/raw_logging_internal (1.20240116.1):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
    - abseil/base/log_severity
  - abseil/base/spinlock_wait (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/strerror (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/errno_saver
  - abseil/base/throw_delegate (1.20240116.1):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/cleanup/cleanup (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/cleanup/cleanup_internal
  - abseil/cleanup/cleanup_internal (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/utility/utility
  - abseil/container/common (1.20240116.1):
    - abseil/meta/type_traits
    - abseil/types/optional
  - abseil/container/common_policy_traits (1.20240116.1):
    - abseil/meta/type_traits
  - abseil/container/compressed_tuple (1.20240116.1):
    - abseil/utility/utility
  - abseil/container/container_memory (1.20240116.1):
    - abseil/base/config
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/container/fixed_array (1.20240116.1):
    - abseil/algorithm/algorithm
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/memory/memory
  - abseil/container/flat_hash_map (1.20240116.1):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_map
    - abseil/memory/memory
  - abseil/container/flat_hash_set (1.20240116.1):
    - abseil/algorithm/container
    - abseil/base/core_headers
    - abseil/container/container_memory
    - abseil/container/hash_function_defaults
    - abseil/container/raw_hash_set
    - abseil/memory/memory
  - abseil/container/hash_function_defaults (1.20240116.1):
    - abseil/base/config
    - abseil/hash/hash
    - abseil/strings/cord
    - abseil/strings/strings
  - abseil/container/hash_policy_traits (1.20240116.1):
    - abseil/container/common_policy_traits
    - abseil/meta/type_traits
  - abseil/container/hashtable_debug_hooks (1.20240116.1):
    - abseil/base/config
  - abseil/container/hashtablez_sampler (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/memory/memory
    - abseil/profiling/exponential_biased
    - abseil/profiling/sample_recorder
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/utility/utility
  - abseil/container/inlined_vector (1.20240116.1):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/inlined_vector_internal
    - abseil/memory/memory
    - abseil/meta/type_traits
  - abseil/container/inlined_vector_internal (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/compressed_tuple
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/span
  - abseil/container/layout (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/debugging/demangle_internal
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
    - abseil/utility/utility
  - abseil/container/raw_hash_map (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/throw_delegate
    - abseil/container/container_memory
    - abseil/container/raw_hash_set
  - abseil/container/raw_hash_set (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/container/common
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/hash_policy_traits
    - abseil/container/hashtable_debug_hooks
    - abseil/container/hashtablez_sampler
    - abseil/hash/hash
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/utility/utility
  - abseil/crc/cpu_detect (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
  - abseil/crc/crc32c (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/crc/cpu_detect
    - abseil/crc/crc_internal
    - abseil/crc/non_temporal_memcpy
    - abseil/strings/str_format
    - abseil/strings/strings
  - abseil/crc/crc_cord_state (1.20240116.1):
    - abseil/base/config
    - abseil/crc/crc32c
    - abseil/numeric/bits
    - abseil/strings/strings
  - abseil/crc/crc_internal (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/base/raw_logging_internal
    - abseil/crc/cpu_detect
    - abseil/memory/memory
    - abseil/numeric/bits
  - abseil/crc/non_temporal_arm_intrinsics (1.20240116.1):
    - abseil/base/config
  - abseil/crc/non_temporal_memcpy (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/crc/non_temporal_arm_intrinsics
  - abseil/debugging/debugging_internal (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/errno_saver
    - abseil/base/raw_logging_internal
  - abseil/debugging/demangle_internal (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/debugging/stacktrace (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
  - abseil/debugging/symbolize (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/debugging_internal
    - abseil/debugging/demangle_internal
    - abseil/strings/strings
  - abseil/flags/commandlineflag (1.20240116.1):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/flags/commandlineflag_internal (1.20240116.1):
    - abseil/base/config
    - abseil/base/fast_type_id
  - abseil/flags/config (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/flags/program_name
    - abseil/strings/strings
    - abseil/synchronization/synchronization
  - abseil/flags/flag (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/config
    - abseil/flags/flag_internal
    - abseil/flags/reflection
    - abseil/strings/strings
  - abseil/flags/flag_internal (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/marshalling
    - abseil/flags/reflection
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/synchronization/synchronization
    - abseil/utility/utility
  - abseil/flags/marshalling (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/log_severity
    - abseil/numeric/int128
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
  - abseil/flags/path_util (1.20240116.1):
    - abseil/base/config
    - abseil/strings/strings
  - abseil/flags/private_handle_accessor (1.20240116.1):
    - abseil/base/config
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/strings/strings
  - abseil/flags/program_name (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/flags/path_util
    - abseil/strings/strings
    - abseil/synchronization/synchronization
  - abseil/flags/reflection (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/container/flat_hash_map
    - abseil/flags/commandlineflag
    - abseil/flags/commandlineflag_internal
    - abseil/flags/config
    - abseil/flags/private_handle_accessor
    - abseil/strings/strings
    - abseil/synchronization/synchronization
  - abseil/functional/any_invocable (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/bind_front (1.20240116.1):
    - abseil/base/base_internal
    - abseil/container/compressed_tuple
    - abseil/meta/type_traits
    - abseil/utility/utility
  - abseil/functional/function_ref (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/core_headers
    - abseil/functional/any_invocable
    - abseil/meta/type_traits
  - abseil/hash/city (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
  - abseil/hash/hash (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/container/fixed_array
    - abseil/functional/function_ref
    - abseil/hash/city
    - abseil/hash/low_level_hash
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/hash/low_level_hash (1.20240116.1):
    - abseil/base/config
    - abseil/base/endian
    - abseil/base/prefetch
    - abseil/numeric/int128
  - abseil/memory (1.20240116.1):
    - abseil/memory/memory (= 1.20240116.1)
  - abseil/memory/memory (1.20240116.1):
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/meta (1.20240116.1):
    - abseil/meta/type_traits (= 1.20240116.1)
  - abseil/meta/type_traits (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/numeric/bits (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/numeric/int128 (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/bits
  - abseil/numeric/representation (1.20240116.1):
    - abseil/base/config
  - abseil/profiling/exponential_biased (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
  - abseil/profiling/sample_recorder (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/synchronization/synchronization
    - abseil/time/time
  - abseil/random/bit_gen_ref (1.20240116.1):
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/random
  - abseil/random/distributions (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/distribution_caller
    - abseil/random/internal/fast_uniform_bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/generate_real
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/traits
    - abseil/random/internal/uniform_helper
    - abseil/random/internal/wide_multiply
    - abseil/strings/strings
  - abseil/random/internal/distribution_caller (1.20240116.1):
    - abseil/base/config
    - abseil/base/fast_type_id
    - abseil/utility/utility
  - abseil/random/internal/fast_uniform_bits (1.20240116.1):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/random/internal/traits
  - abseil/random/internal/fastmath (1.20240116.1):
    - abseil/numeric/bits
  - abseil/random/internal/generate_real (1.20240116.1):
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/random/internal/fastmath
    - abseil/random/internal/traits
  - abseil/random/internal/iostream_state_saver (1.20240116.1):
    - abseil/meta/type_traits
    - abseil/numeric/int128
  - abseil/random/internal/nonsecure_base (1.20240116.1):
    - abseil/base/core_headers
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/types/span
  - abseil/random/internal/pcg_engine (1.20240116.1):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/fastmath
    - abseil/random/internal/iostream_state_saver
  - abseil/random/internal/platform (1.20240116.1):
    - abseil/base/config
  - abseil/random/internal/pool_urbg (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/random/internal/randen
    - abseil/random/internal/seed_material
    - abseil/random/internal/traits
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/random/internal/randen (1.20240116.1):
    - abseil/base/raw_logging_internal
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes
    - abseil/random/internal/randen_slow
  - abseil/random/internal/randen_engine (1.20240116.1):
    - abseil/base/endian
    - abseil/meta/type_traits
    - abseil/random/internal/iostream_state_saver
    - abseil/random/internal/randen
  - abseil/random/internal/randen_hwaes (1.20240116.1):
    - abseil/base/config
    - abseil/random/internal/platform
    - abseil/random/internal/randen_hwaes_impl
  - abseil/random/internal/randen_hwaes_impl (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/randen_slow (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/numeric/int128
    - abseil/random/internal/platform
  - abseil/random/internal/salted_seed_seq (1.20240116.1):
    - abseil/container/inlined_vector
    - abseil/meta/type_traits
    - abseil/random/internal/seed_material
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/seed_material (1.20240116.1):
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/raw_logging_internal
    - abseil/random/internal/fast_uniform_bits
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/random/internal/traits (1.20240116.1):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
  - abseil/random/internal/uniform_helper (1.20240116.1):
    - abseil/base/config
    - abseil/meta/type_traits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/internal/wide_multiply (1.20240116.1):
    - abseil/base/config
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/random/internal/traits
  - abseil/random/random (1.20240116.1):
    - abseil/random/distributions
    - abseil/random/internal/nonsecure_base
    - abseil/random/internal/pcg_engine
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/randen_engine
    - abseil/random/seed_sequences
  - abseil/random/seed_gen_exception (1.20240116.1):
    - abseil/base/config
  - abseil/random/seed_sequences (1.20240116.1):
    - abseil/base/config
    - abseil/random/internal/pool_urbg
    - abseil/random/internal/salted_seed_seq
    - abseil/random/internal/seed_material
    - abseil/random/seed_gen_exception
    - abseil/types/span
  - abseil/status/status (1.20240116.1):
    - abseil/base/atomic_hook
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/no_destructor
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/strerror
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/functional/function_ref
    - abseil/memory/memory
    - abseil/strings/cord
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/status/statusor (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
    - abseil/status/status
    - abseil/strings/has_ostream_operator
    - abseil/strings/str_format
    - abseil/strings/strings
    - abseil/types/variant
    - abseil/utility/utility
  - abseil/strings/charset (1.20240116.1):
    - abseil/base/core_headers
    - abseil/strings/string_view
  - abseil/strings/cord (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/crc/crc32c
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_info
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_scope
    - abseil/strings/cordz_update_tracker
    - abseil/strings/internal
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
  - abseil/strings/cord_internal (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/container/compressed_tuple
    - abseil/container/container_memory
    - abseil/container/inlined_vector
    - abseil/container/layout
    - abseil/crc/crc_cord_state
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/strings/strings
    - abseil/types/span
  - abseil/strings/cordz_functions (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/profiling/exponential_biased
  - abseil/strings/cordz_handle (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/raw_logging_internal
    - abseil/synchronization/synchronization
  - abseil/strings/cordz_info (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/container/inlined_vector
    - abseil/debugging/stacktrace
    - abseil/strings/cord_internal
    - abseil/strings/cordz_functions
    - abseil/strings/cordz_handle
    - abseil/strings/cordz_statistics
    - abseil/strings/cordz_update_tracker
    - abseil/synchronization/synchronization
    - abseil/time/time
    - abseil/types/span
  - abseil/strings/cordz_statistics (1.20240116.1):
    - abseil/base/config
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_scope (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/strings/cord_internal
    - abseil/strings/cordz_info
    - abseil/strings/cordz_update_tracker
  - abseil/strings/cordz_update_tracker (1.20240116.1):
    - abseil/base/config
  - abseil/strings/has_ostream_operator (1.20240116.1):
    - abseil/base/config
  - abseil/strings/internal (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/raw_logging_internal
    - abseil/meta/type_traits
  - abseil/strings/str_format (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/strings/str_format_internal
    - abseil/strings/string_view
    - abseil/types/span
  - abseil/strings/str_format_internal (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/container/fixed_array
    - abseil/container/inlined_vector
    - abseil/functional/function_ref
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/numeric/representation
    - abseil/strings/strings
    - abseil/types/optional
    - abseil/types/span
    - abseil/utility/utility
  - abseil/strings/string_view (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
  - abseil/strings/strings (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/endian
    - abseil/base/nullability
    - abseil/base/raw_logging_internal
    - abseil/base/throw_delegate
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/numeric/bits
    - abseil/numeric/int128
    - abseil/strings/charset
    - abseil/strings/internal
    - abseil/strings/string_view
  - abseil/synchronization/graphcycles_internal (1.20240116.1):
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
  - abseil/synchronization/kernel_timeout_internal (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/time/time
  - abseil/synchronization/synchronization (1.20240116.1):
    - abseil/base/atomic_hook
    - abseil/base/base
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/dynamic_annotations
    - abseil/base/malloc_internal
    - abseil/base/raw_logging_internal
    - abseil/debugging/stacktrace
    - abseil/debugging/symbolize
    - abseil/synchronization/graphcycles_internal
    - abseil/synchronization/kernel_timeout_internal
    - abseil/time/time
  - abseil/time (1.20240116.1):
    - abseil/time/internal (= 1.20240116.1)
    - abseil/time/time (= 1.20240116.1)
  - abseil/time/internal (1.20240116.1):
    - abseil/time/internal/cctz (= 1.20240116.1)
  - abseil/time/internal/cctz (1.20240116.1):
    - abseil/time/internal/cctz/civil_time (= 1.20240116.1)
    - abseil/time/internal/cctz/time_zone (= 1.20240116.1)
  - abseil/time/internal/cctz/civil_time (1.20240116.1):
    - abseil/base/config
  - abseil/time/internal/cctz/time_zone (1.20240116.1):
    - abseil/base/config
    - abseil/time/internal/cctz/civil_time
  - abseil/time/time (1.20240116.1):
    - abseil/base/base
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/raw_logging_internal
    - abseil/numeric/int128
    - abseil/strings/strings
    - abseil/time/internal/cctz/civil_time
    - abseil/time/internal/cctz/time_zone
    - abseil/types/optional
  - abseil/types (1.20240116.1):
    - abseil/types/any (= 1.20240116.1)
    - abseil/types/bad_any_cast (= 1.20240116.1)
    - abseil/types/bad_any_cast_impl (= 1.20240116.1)
    - abseil/types/bad_optional_access (= 1.20240116.1)
    - abseil/types/bad_variant_access (= 1.20240116.1)
    - abseil/types/compare (= 1.20240116.1)
    - abseil/types/optional (= 1.20240116.1)
    - abseil/types/span (= 1.20240116.1)
    - abseil/types/variant (= 1.20240116.1)
  - abseil/types/any (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/fast_type_id
    - abseil/meta/type_traits
    - abseil/types/bad_any_cast
    - abseil/utility/utility
  - abseil/types/bad_any_cast (1.20240116.1):
    - abseil/base/config
    - abseil/types/bad_any_cast_impl
  - abseil/types/bad_any_cast_impl (1.20240116.1):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_optional_access (1.20240116.1):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/bad_variant_access (1.20240116.1):
    - abseil/base/config
    - abseil/base/raw_logging_internal
  - abseil/types/compare (1.20240116.1):
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
  - abseil/types/optional (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/memory/memory
    - abseil/meta/type_traits
    - abseil/types/bad_optional_access
    - abseil/utility/utility
  - abseil/types/span (1.20240116.1):
    - abseil/algorithm/algorithm
    - abseil/base/core_headers
    - abseil/base/nullability
    - abseil/base/throw_delegate
    - abseil/meta/type_traits
  - abseil/types/variant (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/base/core_headers
    - abseil/meta/type_traits
    - abseil/types/bad_variant_access
    - abseil/utility/utility
  - abseil/utility/utility (1.20240116.1):
    - abseil/base/base_internal
    - abseil/base/config
    - abseil/meta/type_traits
  - AppAuth (1.7.3):
    - AppAuth/Core (= 1.7.3)
    - AppAuth/ExternalUserAgent (= 1.7.3)
  - AppAuth/Core (1.7.3)
  - AppAuth/ExternalUserAgent (1.7.3):
    - AppAuth/Core
  - audio_session (0.0.1):
    - Flutter
  - BoringSSL-GRPC (0.0.32):
    - BoringSSL-GRPC/Implementation (= 0.0.32)
    - BoringSSL-GRPC/Interface (= 0.0.32)
  - BoringSSL-GRPC/Implementation (0.0.32):
    - BoringSSL-GRPC/Interface (= 0.0.32)
  - BoringSSL-GRPC/Interface (0.0.32)
  - cloud_firestore (4.15.9):
    - Firebase/Firestore (= 10.22.0)
    - firebase_core
    - Flutter
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - Firebase/Analytics (10.22.0):
    - Firebase/Core
  - Firebase/Auth (10.22.0):
    - Firebase/CoreOnly
    - FirebaseAuth (~> 10.22.0)
  - Firebase/Core (10.22.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.22.0)
  - Firebase/CoreOnly (10.22.0):
    - FirebaseCore (= 10.22.0)
  - Firebase/Firestore (10.22.0):
    - Firebase/CoreOnly
    - FirebaseFirestore (~> 10.22.0)
  - firebase_analytics (10.8.10):
    - Firebase/Analytics (= 10.22.0)
    - firebase_core
    - Flutter
  - firebase_auth (4.17.9):
    - Firebase/Auth (= 10.22.0)
    - firebase_core
    - Flutter
  - firebase_core (2.27.1):
    - Firebase/CoreOnly (= 10.22.0)
    - Flutter
  - FirebaseAnalytics (10.22.0):
    - FirebaseAnalytics/AdIdSupport (= 10.22.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAppCheckInterop (10.23.0)
  - FirebaseAuth (10.22.0):
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GTMSessionFetcher/Core (< 4.0, >= 2.1)
    - RecaptchaInterop (~> 100.0)
  - FirebaseCore (10.22.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.23.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.23.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseFirestore (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseFirestoreInternal (~> 10.17)
    - FirebaseSharedSwift (~> 10.0)
  - FirebaseFirestoreInternal (10.23.0):
    - abseil/algorithm (~> 1.20240116.1)
    - abseil/base (~> 1.20240116.1)
    - abseil/container/flat_hash_map (~> 1.20240116.1)
    - abseil/memory (~> 1.20240116.1)
    - abseil/meta (~> 1.20240116.1)
    - abseil/strings/strings (~> 1.20240116.1)
    - abseil/time (~> 1.20240116.1)
    - abseil/types (~> 1.20240116.1)
    - FirebaseAppCheckInterop (~> 10.17)
    - FirebaseCore (~> 10.0)
    - "gRPC-C++ (~> 1.62.0)"
    - gRPC-Core (~> 1.62.0)
    - leveldb-library (~> 1.22)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseInstallations (10.23.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseSharedSwift (10.23.0)
  - Flutter (1.0.0)
  - flutter_email_sender (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_timezone (0.0.1):
    - Flutter
  - Google-Mobile-Ads-SDK (10.11.0):
    - GoogleAppMeasurement (< 11.0, >= 7.0)
    - GoogleUserMessagingPlatform (>= 1.1)
  - google_mobile_ads (1.0.0):
    - Flutter
    - Google-Mobile-Ads-SDK (~> 10.11.0)
    - webview_flutter_wkwebview
  - google_sign_in_ios (0.0.1):
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.0)
  - GoogleAppMeasurement (10.22.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.22.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.22.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleSignIn (7.0.0):
    - AppAuth (~> 1.5)
    - GTMAppAuth (< 3.0, >= 1.3)
    - GTMSessionFetcher/Core (< 4.0, >= 1.1)
  - GoogleUserMessagingPlatform (2.3.0)
  - GoogleUtilities/AppDelegateSwizzler (7.13.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.0):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.0)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.0)
  - GoogleUtilities/Reachability (7.13.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.0):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - "gRPC-C++ (1.62.1)":
    - "gRPC-C++/Implementation (= 1.62.1)"
    - "gRPC-C++/Interface (= 1.62.1)"
  - "gRPC-C++/Implementation (1.62.1)":
    - abseil/algorithm/container (= 1.20240116.1)
    - abseil/base/base (= 1.20240116.1)
    - abseil/base/config (= 1.20240116.1)
    - abseil/base/core_headers (= 1.20240116.1)
    - abseil/cleanup/cleanup (= 1.20240116.1)
    - abseil/container/flat_hash_map (= 1.20240116.1)
    - abseil/container/flat_hash_set (= 1.20240116.1)
    - abseil/container/inlined_vector (= 1.20240116.1)
    - abseil/flags/flag (= 1.20240116.1)
    - abseil/flags/marshalling (= 1.20240116.1)
    - abseil/functional/any_invocable (= 1.20240116.1)
    - abseil/functional/bind_front (= 1.20240116.1)
    - abseil/functional/function_ref (= 1.20240116.1)
    - abseil/hash/hash (= 1.20240116.1)
    - abseil/memory/memory (= 1.20240116.1)
    - abseil/meta/type_traits (= 1.20240116.1)
    - abseil/random/bit_gen_ref (= 1.20240116.1)
    - abseil/random/distributions (= 1.20240116.1)
    - abseil/random/random (= 1.20240116.1)
    - abseil/status/status (= 1.20240116.1)
    - abseil/status/statusor (= 1.20240116.1)
    - abseil/strings/cord (= 1.20240116.1)
    - abseil/strings/str_format (= 1.20240116.1)
    - abseil/strings/strings (= 1.20240116.1)
    - abseil/synchronization/synchronization (= 1.20240116.1)
    - abseil/time/time (= 1.20240116.1)
    - abseil/types/optional (= 1.20240116.1)
    - abseil/types/span (= 1.20240116.1)
    - abseil/types/variant (= 1.20240116.1)
    - abseil/utility/utility (= 1.20240116.1)
    - "gRPC-C++/Interface (= 1.62.1)"
    - "gRPC-C++/Privacy (= 1.62.1)"
    - gRPC-Core (= 1.62.1)
  - "gRPC-C++/Interface (1.62.1)"
  - "gRPC-C++/Privacy (1.62.1)"
  - gRPC-Core (1.62.1):
    - gRPC-Core/Implementation (= 1.62.1)
    - gRPC-Core/Interface (= 1.62.1)
  - gRPC-Core/Implementation (1.62.1):
    - abseil/algorithm/container (= 1.20240116.1)
    - abseil/base/base (= 1.20240116.1)
    - abseil/base/config (= 1.20240116.1)
    - abseil/base/core_headers (= 1.20240116.1)
    - abseil/cleanup/cleanup (= 1.20240116.1)
    - abseil/container/flat_hash_map (= 1.20240116.1)
    - abseil/container/flat_hash_set (= 1.20240116.1)
    - abseil/container/inlined_vector (= 1.20240116.1)
    - abseil/flags/flag (= 1.20240116.1)
    - abseil/flags/marshalling (= 1.20240116.1)
    - abseil/functional/any_invocable (= 1.20240116.1)
    - abseil/functional/bind_front (= 1.20240116.1)
    - abseil/functional/function_ref (= 1.20240116.1)
    - abseil/hash/hash (= 1.20240116.1)
    - abseil/memory/memory (= 1.20240116.1)
    - abseil/meta/type_traits (= 1.20240116.1)
    - abseil/random/bit_gen_ref (= 1.20240116.1)
    - abseil/random/distributions (= 1.20240116.1)
    - abseil/random/random (= 1.20240116.1)
    - abseil/status/status (= 1.20240116.1)
    - abseil/status/statusor (= 1.20240116.1)
    - abseil/strings/cord (= 1.20240116.1)
    - abseil/strings/str_format (= 1.20240116.1)
    - abseil/strings/strings (= 1.20240116.1)
    - abseil/synchronization/synchronization (= 1.20240116.1)
    - abseil/time/time (= 1.20240116.1)
    - abseil/types/optional (= 1.20240116.1)
    - abseil/types/span (= 1.20240116.1)
    - abseil/types/variant (= 1.20240116.1)
    - abseil/utility/utility (= 1.20240116.1)
    - BoringSSL-GRPC (= 0.0.32)
    - gRPC-Core/Interface (= 1.62.1)
    - gRPC-Core/Privacy (= 1.62.1)
  - gRPC-Core/Interface (1.62.1)
  - gRPC-Core/Privacy (1.62.1)
  - GTMAppAuth (2.0.0):
    - AppAuth/Core (~> 1.6)
    - GTMSessionFetcher/Core (< 4.0, >= 1.5)
  - GTMSessionFetcher/Core (3.3.1)
  - just_audio (0.0.1):
    - Flutter
  - leveldb-library (1.22.4)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - purchases_flutter (6.23.0):
    - Flutter
    - PurchasesHybridCommon (= 9.9.0)
  - PurchasesHybridCommon (9.9.0):
    - RevenueCat (= 4.37.0)
  - ReachabilitySwift (5.2.1)
  - RecaptchaInterop (100.0.0)
  - RevenueCat (4.37.0)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - cloud_firestore (from `.symlinks/plugins/cloud_firestore/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_auth (from `.symlinks/plugins/firebase_auth/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - Flutter (from `Flutter`)
  - flutter_email_sender (from `.symlinks/plugins/flutter_email_sender/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_timezone (from `.symlinks/plugins/flutter_timezone/ios`)
  - google_mobile_ads (from `.symlinks/plugins/google_mobile_ads/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - purchases_flutter (from `.symlinks/plugins/purchases_flutter/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - abseil
    - AppAuth
    - BoringSSL-GRPC
    - Firebase
    - FirebaseAnalytics
    - FirebaseAppCheckInterop
    - FirebaseAuth
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseFirestore
    - FirebaseFirestoreInternal
    - FirebaseInstallations
    - FirebaseSharedSwift
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleSignIn
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - "gRPC-C++"
    - gRPC-Core
    - GTMAppAuth
    - GTMSessionFetcher
    - leveldb-library
    - nanopb
    - PromisesObjC
    - PurchasesHybridCommon
    - ReachabilitySwift
    - RecaptchaInterop
    - RevenueCat

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  cloud_firestore:
    :path: ".symlinks/plugins/cloud_firestore/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_auth:
    :path: ".symlinks/plugins/firebase_auth/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  Flutter:
    :path: Flutter
  flutter_email_sender:
    :path: ".symlinks/plugins/flutter_email_sender/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_timezone:
    :path: ".symlinks/plugins/flutter_timezone/ios"
  google_mobile_ads:
    :path: ".symlinks/plugins/google_mobile_ads/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  purchases_flutter:
    :path: ".symlinks/plugins/purchases_flutter/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  abseil: ebec4f56469dd7ce9ab08683c0319a68aa0ad86e
  AppAuth: a13994980c1ec792f7e2e665acd4d4aa6be43240
  audio_session: 4f3e461722055d21515cf3261b64c973c062f345
  BoringSSL-GRPC: 1e2348957acdbcad360b80a264a90799984b2ba6
  cloud_firestore: 9d69182af1d677062f005584ca5470a19e1f68e7
  connectivity_plus: 413a8857dd5d9f1c399a39130850d02fe0feaf7e
  Firebase: 797fd7297b7e1be954432743a0b3f90038e45a71
  firebase_analytics: 773f04fad34c0a4e442deb5bcfb6ffc8102b9221
  firebase_auth: 610184b4f624545f81e0416bca60642b6818f27f
  firebase_core: d6dfb4cb86a9ebd92464bb8736075fe967211c97
  FirebaseAnalytics: 8d0ff929c63b7f72260f332b86ccf569776b75d3
  FirebaseAppCheckInterop: a1955ce8c30f38f87e7d091630e871e91154d65d
  FirebaseAuth: bbe4c68f958504ba9e54aee181adbdf5b664fbc6
  FirebaseCore: 0326ec9b05fbed8f8716cddbf0e36894a13837f7
  FirebaseCoreExtension: cb88851781a24e031d1b58e0bd01eb1f46b044b5
  FirebaseCoreInternal: 6a292e6f0bece1243a737e81556e56e5e19282e3
  FirebaseFirestore: 16cb8a85fc29da272deaed22a101e24703251da9
  FirebaseFirestoreInternal: 627b23f682c1c2aad38ba1345ed3ca6574c5a89c
  FirebaseInstallations: 42d6ead4605d6eafb3b6683674e80e18eb6f2c35
  FirebaseSharedSwift: c92645b392db3c41a83a0aa967de16f8bad25568
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_email_sender: 02d7443217d8c41483223627972bfdc09f74276b
  flutter_local_notifications: 4cde75091f6327eb8517fa068a0a5950212d2086
  flutter_timezone: ffb07bdad3c6276af8dada0f11978d8a1f8a20bb
  Google-Mobile-Ads-SDK: 58b4fda3f9758fc1ed210aa5cf7777b5eb55d47e
  google_mobile_ads: 511febb4768edc860ee455a9e201ff52de385908
  google_sign_in_ios: 989eea5abe94af62050782714daf920be883d4a2
  GoogleAppMeasurement: ccefe3eac9b0aa27f96066809fb1a7fe4b462626
  GoogleSignIn: b232380cf495a429b8095d3178a8d5855b42e842
  GoogleUserMessagingPlatform: b1ad7bb1ee3ce64749d4fec24f667b36e45c396c
  GoogleUtilities: d053d902a8edaa9904e1bd00c37535385b8ed152
  "gRPC-C++": 12f33a422dcab88dcd0c53e52cd549a929f0f244
  gRPC-Core: 6ec9002832e1e22c5bb8c54994b050b0ee4205c6
  GTMAppAuth: 99fb010047ba3973b7026e45393f51f27ab965ae
  GTMSessionFetcher: 8a1b34ad97ebe6f909fb8b9b77fba99943007556
  just_audio: baa7252489dbcf47a4c7cc9ca663e9661c99aafa
  leveldb-library: 06a69cc7582d64b29424a63e085e683cc188230a
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  purchases_flutter: 496a234392738f9bb0f4b8bff9a1b4596d1ac896
  PurchasesHybridCommon: 42af1edb779902793424e6fc1c159727f7d56595
  ReachabilitySwift: 5ae15e16814b5f9ef568963fb2c87aeb49158c66
  RecaptchaInterop: 7d1a4a01a6b2cb1610a47ef3f85f0c411434cb21
  RevenueCat: b4fb058d7ced6a37327f6bc9636752dbc4a7235b
  shared_preferences_foundation: b4c3b4cddf1c21f02770737f147a3f5da9d39695
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  url_launcher_ios: 6116280ddcfe98ab8820085d8d76ae7449447586
  webview_flutter_wkwebview: be0f0d33777f1bfd0c9fdcb594786704dbf65f36

PODFILE CHECKSUM: bd4ee170f0bff337816f4ed756a44da383e7e0fa

COCOAPODS: 1.15.2
