import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/services/notification_service.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:purchases_flutter/purchases_flutter.dart';

import 'dart:io' show Platform;

import 'analytics_controller.dart';
import 'auth_controller.dart';
import 'index_controller.dart';

class PurchaseApi {
  static String link = '';
  static String userID = '';

  static Future init() async {
    String googleApiKey = dotenv.get("ANDROID_REVCAT_API_KEY", fallback: "");

    String appleApiKey = dotenv.get("IOS_REVCAT_API_KEY", fallback: "");
    AuthController authController = Get.find();
    await Purchases.setLogLevel(LogLevel.error);

    if (Platform.isAndroid) {
      await Purchases.configure(PurchasesConfiguration(googleApiKey)
        ..appUserID = authController.user!.uid);
    } else if (Platform.isIOS) {
      await Purchases.configure(PurchasesConfiguration(appleApiKey)
        ..appUserID = authController.user!.uid);
    }
  }

  static Future<List<Offering>> fetchOffers() async {
    try {
      final offerings = await Purchases.getOfferings();
      final current = offerings.current;
      return current == null ? [] : [current];
    } catch (e) {
      return [];
    }
  }

  static premiumCheckerUpdate() {
    ProfileController profileController = Get.find();

    Purchases.addCustomerInfoUpdateListener((customerInfo) async {
      final isSubscribed =
          customerInfo.entitlements.active.containsKey("Premium");

      if (isSubscribed) {
        profileController.isPremiumUser.value = true;
        profileController.update();
        profileController.updateUserSubscription();
      } else {
        NotificationService().cancelNotification();
        profileController.isPremiumUser.value = false;
        profileController.update();
        profileController.updateUserSubscription();
      }
    });
  }

  static Future purchasePackage(
      Package package, bool isFreeTimeSelected) async {
    ProfileController profileController = Get.find();
    final IndexController indexController = Get.find();
    final AnalticsController analticsController = AnalticsController();
    try {
      await Purchases.purchasePackage(package);
      void customerInfoUpdateListener(CustomerInfo customerInfo) async {
        // final bool trialEligible = customerInfo.entitlements.all.isEmpty;

        final isSubscribed =
            customerInfo.entitlements.active.containsKey("Premium");

        if (isSubscribed) {
          if (package.storeProduct.defaultOption!.billingPeriod!.iso8601 ==
              'P1M') {
            analticsController.subPlanMonthlyAnalyticsUpdate();
          } else {
            analticsController.subPlanYearlyAnalyticsUpdate();
          }
          // if (trialEligible && isFreeTimeSelected) {
          //   NotificationService().scheduleNotification();
          // }

          profileController.updateUserSubscription();
        }
      }

      profileController.userr.update((user) {
        user?.isPremiumUser = true;
      });
      indexController.selectedIndex.value = 0;

      Purchases.addCustomerInfoUpdateListener(customerInfoUpdateListener);
    } catch (e) {
      if (e is PlatformException && e.code == 'PurchaseCancelledError') {
        // User cancelled the purchase – ignore silently
        return;
      }

      getErrorSnackBar('Nešto je pošlo po zlu, Molimo pokušajte ponovo');
    }
  }

  static getLink() async {
    Purchases.addCustomerInfoUpdateListener((customerInfo) async {
      final customerInfo = await Purchases.getCustomerInfo();
      userID = customerInfo.originalAppUserId;
      link = customerInfo.managementURL!;
    });
  }
}
