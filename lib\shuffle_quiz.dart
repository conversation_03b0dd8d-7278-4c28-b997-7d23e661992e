import 'dart:io';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/models/question_model.dart';
import 'package:bibl/models/quiz_model.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/views/quiz_result.dart';
import 'package:flutter/material.dart';
import 'package:flutter_card_swiper/flutter_card_swiper.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'controllers/analytics_controller.dart';
import 'controllers/lesson_controller.dart';
import 'widgets/arrow_back_button_widget.dart';
import 'widgets/customappbar.dart';
import 'widgets/local_image_widget.dart';

class ShuffleQuiz extends StatefulWidget {
  final ShuffleQuizModel quiz;

  const ShuffleQuiz({Key? key, required this.quiz}) : super(key: key);

  @override
  State<ShuffleQuiz> createState() => _ShuffleQuizState();
}

class _ShuffleQuizState extends State<ShuffleQuiz> {
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();
  final AnalticsController analyticsController = AnalticsController();
  int currentPage = 0;
  late List<bool?> selectedOptions;

  @override
  void initState() {
    super.initState();
    analyticsController.quizStartedAnalyticsUpdate(widget.quiz.quizName!);
    selectedOptions = List.filled(widget.quiz.questionsList!.length, null);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (profileController.userr.value.isPremiumUser! == false) {
        profileController.updatedOpenedQuizesAndArticles(widget.quiz.quizId!);
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  CustomAppBar quizAppbarWidget() {
    // Calculate progress as a fraction of the total questions
    double progress = (currentPage + 1) / widget.quiz.questionsList!.length;

    return CustomAppBar(
      widget: Row(
        children: [
          const SizedBox(width: 10),
          arrowBackButtonWidget(),
          const SizedBox(width: 10),
          Expanded(
            child: LinearProgressIndicator(
              value: progress.clamp(
                  0.0, 1.0), // Ensure progress is between 0 and 1
              backgroundColor: Colors.white.withValues(alpha: 0.2),
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
              minHeight: 6,
            ),
          ),
          const SizedBox(width: 10),
          Container(
            width: 60,
            height: 28,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
              borderRadius: BorderRadius.circular(60),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset('assets/svgs/cup_icon.svg'),
                const SizedBox(width: 5),
                const Txt(
                  txt: '5+',
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  fontColor: Colors.white,
                ),
              ],
            ),
          ),
          const SizedBox(width: 30),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: true,
      top: false,
      child: Scaffold(
        appBar: quizAppbarWidget(),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Txt(
                txt: 'Tačno ili Netačno',
                fontSize: 20,
                maxLines: 5,
              ),
              const Txt(
                txt: 'Swipe levo za netačno, desno za Tačno',
                fontColor: grey2Color,
                maxLines: 5,
                fontWeight: FontWeight.normal,
                fontSize: 14,
              ),
              const SizedBox(height: 30),
              Expanded(
                child: CardSwiper(
                  isLoop: false,
                  scale: 0,
                  threshold: 100,
                  onSwipe: (previousIndex, currentIndex, direction) {
                    setState(() {
                      if (direction == CardSwiperDirection.right) {
                        selectedOptions[previousIndex] = true;
                      } else if (direction == CardSwiperDirection.left) {
                        selectedOptions[previousIndex] = false;
                      }
                      currentPage = currentIndex ?? previousIndex + 1;
                    });

                    if (previousIndex ==
                        widget.quiz.questionsList!.length - 1) {
                      Get.off(() => QuizResult(
                            finishedShuffleQuizModel: widget.quiz,
                            selectedAnswersOfShuffleQuiz: selectedOptions,
                          ));
                    }

                    return true;
                  },
                  allowedSwipeDirection: const AllowedSwipeDirection.symmetric(
                      vertical: false, horizontal: true),
                  padding: const EdgeInsets.all(0),
                  cardsCount: widget.quiz.questionsList!.length,
                  cardBuilder: (context, index, horizontalOffsetPercentage,
                      verticalOffsetPercentage) {
                    return _buildQuestionCard(
                        index, widget.quiz.questionsList![index]);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build the question card
  Widget _buildQuestionCard(int index, ShuffleQuizQuestionModel question) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(18),
        border: Border.all(color: Colors.black.withValues(alpha: 0.2)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(18),
        child: Stack(
          children: [
            Positioned.fill(
              child: FutureBuilder<File?>(
                future: lessonController.fetchAndCacheImage(
                  question.qsImage!,
                  "${widget.quiz.quizId}_${question.qsNo}.jpg",
                ),
                builder: (context, snapshot) {
                  return LocalImageWidget(imageFile: snapshot.data);
                },
              ),
            ),
            Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(18),
                    color: const Color(0xffF8EFFF).withValues(alpha: 0.7),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _buildQuestionDetails(index, question),
                      _buildSwipeActions(),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  // Build question details section
  Widget _buildQuestionDetails(int index, ShuffleQuizQuestionModel question) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Txt(
            txt: 'Pitanje ${index + 1} OD ${widget.quiz.questionsList!.length}',
            fontSize: 14,
          ),
          const SizedBox(height: 8),
          Txt(
            txt: question.question!,
            textAlign: TextAlign.center,
            maxLines: 10,
            fontSize: 18,
          ),
        ],
      ),
    );
  }

// Build swipe action controls with draggable functionality
  Widget _buildSwipeActions() {
    return Align(
      alignment: Alignment.bottomCenter,
      child: SizedBox(
        height: 90,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Left background container
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  optionWidget('Netačno', 0xffFF0505, false),
                  optionWidget('Tačno', 0xff7CE099, true),
                ],
              ),
            ),
            gradientCircle(),
          ],
        ),
      ),
    );
  }

  Container gradientCircle() {
    return Container(
      width: 80, // Set width and height to match the CircleAvatar's size
      height: 80,
      decoration: BoxDecoration(
        shape: BoxShape.circle, // Ensure the container is round
        border: Border.all(color: Colors.white),
        gradient: const LinearGradient(
          colors: [
            Color(0xffFF0505), // Red
            Color(0xff7CE099), // Green
          ],
          begin: Alignment.topLeft, // Gradient starts from top left
          end: Alignment.bottomRight, // Gradient ends at bottom right
        ),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Icon(Icons.keyboard_double_arrow_left, color: Colors.white),
          Icon(Icons.keyboard_double_arrow_right, color: Colors.white),
        ],
      ),
    );
  }

  Expanded optionWidget(String text, int color, bool isleft) {
    return Expanded(
      child: Container(
        height: 70,
        decoration: BoxDecoration(
            color: Color(color),
            borderRadius: !isleft
                ? const BorderRadius.only(
                    topLeft: Radius.circular(100),
                    bottomLeft: Radius.circular(100))
                : const BorderRadius.only(
                    topRight: Radius.circular(100),
                    bottomRight: Radius.circular(100))),
        child: Center(
          child: Txt(
            txt: text,
            fontColor: Colors.white,
            fontWeight: FontWeight.normal,
            fontSize: 14,
          ),
        ),
      ),
    );
  }
}
