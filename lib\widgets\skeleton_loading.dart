import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:bibl/res/style.dart';

/// Professional skeleton loading widgets for maintaining stable layouts
/// while content loads, preventing content jumping
class SkeletonLoading {
  
  /// Skeleton for content cards (lessons, quizzes)
  static Widget contentCard({
    double height = 200,
    double width = double.infinity,
    BorderRadius? borderRadius,
  }) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.withValues(alpha: 0.1),
      highlightColor: Colors.grey.withValues(alpha: 0.05),
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius ?? BorderRadius.circular(14),
        ),
      ),
    );
  }

  /// Skeleton for text lines
  static Widget textLine({
    double height = 16,
    double width = double.infinity,
    BorderRadius? borderRadius,
  }) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.withValues(alpha: 0.1),
      highlightColor: Colors.grey.withValues(alpha: 0.05),
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: borderRadius ?? BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Skeleton for circular elements (avatars, icons)
  static Widget circle({
    double size = 40,
  }) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.withValues(alpha: 0.1),
      highlightColor: Colors.grey.withValues(alpha: 0.05),
      child: Container(
        height: size,
        width: size,
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  /// Complete skeleton for homepage content list
  static Widget homeContentList() {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 5, // Show 5 skeleton items
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.fromLTRB(16, 10, 16, 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image skeleton
              contentCard(height: 180),
              const SizedBox(height: 12),
              
              // Title skeleton
              textLine(height: 20, width: 250),
              const SizedBox(height: 8),
              
              // Subtitle skeleton
              textLine(height: 16, width: 180),
              const SizedBox(height: 12),
              
              // Tags skeleton
              Row(
                children: [
                  textLine(height: 24, width: 60),
                  const SizedBox(width: 8),
                  textLine(height: 24, width: 80),
                  const SizedBox(width: 8),
                  textLine(height: 24, width: 50),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// Skeleton for interests/categories widget
  static Widget interestsWidget() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title skeleton
          textLine(height: 24, width: 200),
          const SizedBox(height: 16),
          
          // Categories grid skeleton
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 3,
            ),
            itemCount: 6,
            itemBuilder: (context, index) {
              return contentCard(height: 40);
            },
          ),
        ],
      ),
    );
  }

  /// Skeleton for days widget
  static Widget daysWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: List.generate(7, (index) {
          return Column(
            children: [
              textLine(height: 16, width: 30),
              const SizedBox(height: 8),
              circle(size: 32),
            ],
          );
        }),
      ),
    );
  }

  /// Complete skeleton for homepage
  static Widget homePage() {
    return Column(
      children: [
        // App bar skeleton
        Container(
          height: 120,
          decoration: BoxDecoration(
            gradient: mainColorsGradient,
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: [
                  circle(size: 40),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        textLine(height: 16, width: 120),
                        const SizedBox(height: 4),
                        textLine(height: 14, width: 80),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      circle(size: 24),
                      const SizedBox(width: 8),
                      textLine(height: 16, width: 30),
                      const SizedBox(width: 16),
                      circle(size: 24),
                      const SizedBox(width: 8),
                      textLine(height: 16, width: 40),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        
        // Days widget skeleton
        const SizedBox(height: 16),
        daysWidget(),
        const SizedBox(height: 20),
        
        // Content list skeleton
        Expanded(
          child: homeContentList(),
        ),
      ],
    );
  }
}
