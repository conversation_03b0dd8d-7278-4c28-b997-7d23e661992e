import 'package:cloud_firestore/cloud_firestore.dart';

class QuestionModel {
  String? question;
  String? correctOption;
  int? qsNo;
  List? options;

  QuestionModel({
    this.question,
    this.correctOption,
    this.qsNo,
    this.options,
  });
  Map<String, dynamic> toMap() {
    return {
      'question': question,
      'correctOption': correctOption,
      'qsNo': qsNo ?? 0, // Default value if null
      'options': options?.join(',') ?? '',
    };
  }

  static QuestionModel fromMap(Map<String, dynamic> map) {
    return QuestionModel(
      question: map['question'] as String? ?? '', // Provide default value
      correctOption: map['correctOption'] as String? ?? '',
      qsNo: map['qsNo'] as int? ?? 0,
      options:
          (map['options'] as String?)?.split(',') ?? [], // Handle null list
    );
  }

  factory QuestionModel.fromJson(Map<String, dynamic> json) {
    return QuestionModel(
      question: json['question'],
      correctOption: json['correctOption'],
      options: json['options'],
      qsNo: json['qsNo'],
    );
  }

  Map<String, dynamic> toJson() => {
        "question": question,
        "correctOption": correctOption,
        "options": options,
        "qsNo": qsNo,
      };

  static QuestionModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    return QuestionModel(
      question: snapshot['question'],
      correctOption: snapshot['correctOption'],
      options: snapshot['options'],
      qsNo: snapshot['qsNo'],
    );
  }
}

class ShuffleQuizQuestionModel {
  String? question;
  String? qsImage;
  bool? correctOption;
  int? qsNo;

  ShuffleQuizQuestionModel({
    this.question,
    this.qsImage,
    this.correctOption,
    this.qsNo,
  });

  /// Convert ShuffleQuizQuestionModel to a map for SQLite
  Map<String, dynamic> toMap() {
    return {
      'question': question,
      'qsImage': qsImage,
      'correctOption': correctOption == true ? 1 : 0, // Convert bool to int
      'qsNo': qsNo,
    };
  }

  /// Create ShuffleQuizQuestionModel from a database map
  static ShuffleQuizQuestionModel fromMap(Map<String, dynamic> map) {
    return ShuffleQuizQuestionModel(
      question: map['question'],
      qsImage: map['qsImage'],
      correctOption: map['correctOption'] == 1, // Convert int to bool
      qsNo: map['qsNo'],
    );
  }

  factory ShuffleQuizQuestionModel.fromJson(Map<String, dynamic> json) {
    return ShuffleQuizQuestionModel(
      question: json['question'],
      qsImage: json['qsImage'],
      correctOption: json['correctOption'],
      qsNo: json['qsNo'],
    );
  }

  Map<String, dynamic> toJson() => {
        "question": question,
        "qsImage": qsImage,
        "correctOption": correctOption,
        "qsNo": qsNo,
      };

  static ShuffleQuizQuestionModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;

    return ShuffleQuizQuestionModel(
      question: snapshot['question'],
      qsImage: snapshot['qsImage'],
      correctOption: snapshot['correctOption'] is String
          ? true
          : snapshot['correctOption'],
      qsNo: snapshot['qsNo'],
    );
  }
}
