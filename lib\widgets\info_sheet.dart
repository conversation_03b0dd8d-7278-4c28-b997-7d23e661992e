// ignore_for_file: unused_import

import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/cross_widget.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/controllers/category_controller.dart';

import '../controllers/auth_controller.dart';

class InfoSheet extends StatefulWidget {
  const InfoSheet({super.key});

  @override
  InfoSheeState createState() => InfoSheeState();
}

class InfoSheeState extends State<InfoSheet> {
  final AuthController authController = Get.find();
  @override
  void initState() {
    super.initState();
    _setHasSeenInfoSheetFalse();
  }

  Future<void> _setHasSeenInfoSheetFalse() async {
    final firestore = FirebaseFirestore.instance;
    final uid = authController.user!.uid;

    final userDocRef = firestore.collection('users').doc(uid);
    final userDoc = await userDocRef.get();

    if (userDoc.exists) {
      // Field set kar do to false
      await userDocRef.update({'hasSeenInfoSheet': true});
    } else {
      // Document hi nahi hai, create with the field
      await userDocRef.set({'hasSeenInfoSheet': true});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Wrap(
        alignment: WrapAlignment.center,
        children: [
          Row(
            children: [
              IconButton(onPressed: null, icon: Container()),
              const Spacer(),
              Container(
                height: 4,
                width: Get.width * 0.4,
                decoration: BoxDecoration(
                    color: grey2Color, borderRadius: BorderRadius.circular(50)),
              ),
              const Spacer(),
              crossWidget(),
            ],
          ),
          SvgPicture.asset('assets/svgs/flames.svg'),
          const Padding(
            padding: EdgeInsets.only(top: 20, bottom: 20),
            child: Txt(
              txt: 'Zaradi više neurona svakog dana',
              fontSize: 20,
              textAlign: TextAlign.center,
              maxLines: 3,
            ),
          ),
          const Padding(
            padding: EdgeInsets.only(bottom: 40),
            child: Txt(
              txt:
                  'Završite barem jednu lekciju ili kviz dnevno da biste održali svoj niz i povećavali nagradne neurone. Preskakanje jednog dana resetuje vaš niz na nulu.',
              fontSize: 14,
              maxLines: 10,
              fontWeight: FontWeight.normal,
              fontColor: grey2Color,
              textAlign: TextAlign.center,
            ),
          ),
          buttonContainer(text: 'Razumem', onTap: () => Get.back())
        ],
      ),
    );
  }
}
