// ignore_for_file: unnecessary_string_interpolations

import 'package:bibl/models/league_user_model.dart';
import 'package:bibl/res/style.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'controllers/profile_controller.dart';
import 'services/leaderboard_service.dart';
import 'widgets/customappbar.dart';
import 'widgets/leaderboard_widgets.dart';
import 'widgets/league_clock_widget.dart';

class Leaderboard extends StatefulWidget {
  const Leaderboard({super.key});

  @override
  State<Leaderboard> createState() => _LeaderboardState();
}

class _LeaderboardState extends State<Leaderboard> {
  final ProfileController profileController = Get.find();

  Future<void> _assignUserToBronzana(String userId, String username) async {
    try {
      final leaderboardService = LeaderboardService(userId);
      await leaderboardService.addUserToLeague(
        username: username,
        targetLeague: '<PERSON><PERSON><PERSON><PERSON>',
      );
    } catch (e) {
      debugPrint('Error assigning user to <PERSON><PERSON>zana: $e');
      Get.snackbar('Error', 'Failed to assign to Bronzana League: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;

    if (user == null) {
      return const Scaffold(
        body: Center(child: Text('Please log in to view the leaderboard.')),
      );
    }

    final userID = user.uid;

    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          isBackButton: false,
          widget: null,
          title: 'umniLab lige',
        ),
        body: StreamBuilder<DocumentSnapshot>(
          stream: FirebaseFirestore.instance
              .collection('users')
              .doc(userID)
              .snapshots(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(
                  child: CircularProgressIndicator(color: mainColor));
            }

            if (snapshot.hasError) {
              return const Center(child: Text('Error loading user data.'));
            }

            if (!snapshot.hasData || !snapshot.data!.exists) {
              return const Center(child: Text('User not found.'));
            }

            final userData = snapshot.data!.data() as Map<String, dynamic>;
            String league = userData['league'] ?? 'Bronzana';

            // If league is empty, assign to Bronzana
            if (league.isEmpty) {
              league = 'Bronzana';
              WidgetsBinding.instance.addPostFrameCallback((_) async {
                try {
                  await _assignUserToBronzana(
                      userID, profileController.userr.value.uniqueName ?? '');
                } catch (e) {
                  debugPrint('Error assigning user to Bronzana: $e');
                }
              });
            }

            return Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Positioned.fill(
                        child: Column(
                          children: [
                            const SizedBox(height: 16),
                            const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Txt(
                                  txt:
                                      'Click this button to check what happens after the week ends',
                                  maxLines: 2,
                                  fontSize: 16),
                            ),
                            ElevatedButton(
                              onPressed: () async {
                                try {
                                  await FirebaseFunctions.instance
                                      .httpsCallable('manualUpdateLeaderboard')
                                      .call();
                                  Get.snackbar('Success',
                                      'Leaderboard updated successfully!');
                                } catch (e) {
                                  Get.snackbar('Error',
                                      'Failed to update leaderboard: $e');
                                }
                              },
                              child: const Text('Update Leaderboard'),
                            ),
                            Expanded(
                              child: StreamBuilder<DocumentSnapshot>(
                                stream: FirebaseFirestore.instance
                                    .collection('users')
                                    .doc(userID)
                                    .snapshots(),
                                builder: (context, userSnapshot) {
                                  if (userSnapshot.connectionState ==
                                      ConnectionState.waiting) {
                                    return const Center(
                                        child: CircularProgressIndicator(
                                            color: mainColor));
                                  }

                                  if (userSnapshot.hasError ||
                                      !userSnapshot.hasData ||
                                      !userSnapshot.data!.exists) {
                                    return const Center(
                                        child:
                                            Text('Error loading user data.'));
                                  }

                                  final userData = userSnapshot.data!.data()
                                      as Map<String, dynamic>;
                                  final userGroup =
                                      userData['leagueGroup'] as String?;

                                  // If user has no group, assign them to one
                                  if (userGroup == null) {
                                    WidgetsBinding.instance
                                        .addPostFrameCallback((_) async {
                                      try {
                                        final leaderboardService =
                                            LeaderboardService(userID);
                                        await leaderboardService
                                            .addUserToLeague(
                                          username: profileController
                                                  .userr.value.uniqueName ??
                                              'Unknown',
                                          targetLeague: league,
                                        );
                                      } catch (e) {
                                        debugPrint(
                                            'Error adding user to league: $e');
                                      }
                                    });
                                    return const Center(
                                        child: CircularProgressIndicator(
                                            color: mainColor));
                                  }

                                  // Now get the leaderboard for the user's group
                                  return StreamBuilder<QuerySnapshot>(
                                    stream: FirebaseFirestore.instance
                                        .collection('leaderboards')
                                        .doc(league)
                                        .collection('groups')
                                        .doc(userGroup)
                                        .collection('players')
                                        .orderBy('score', descending: true)
                                        .orderBy('lastUpdated',
                                            descending: false)
                                        .snapshots(),
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState ==
                                          ConnectionState.waiting) {
                                        return const Center(
                                            child: CircularProgressIndicator(
                                                color: mainColor));
                                      }

                                      if (snapshot.hasError) {
                                        return const Center(
                                            child: Text(
                                                'Error loading leaderboard.'));
                                      }

                                      if (!snapshot.hasData ||
                                          snapshot.data!.docs.isEmpty) {
                                        return const Center(
                                            child: Text(
                                                'No players found in your group.'));
                                      }

                                      final players = snapshot.data!.docs
                                          .map((doc) =>
                                              LeaguePlayerModel.fromSnap(doc))
                                          .toList();

                                      return ListView(
                                        padding: const EdgeInsets.all(16.0),
                                        children: [
                                          // Group info header
                                          Container(
                                            margin: const EdgeInsets.only(
                                                bottom: 16),
                                            padding: const EdgeInsets.all(12),
                                            decoration: BoxDecoration(
                                              color: mainColor.withValues(
                                                  alpha: 0.1),
                                              borderRadius:
                                                  BorderRadius.circular(8),
                                            ),
                                            child: Text(
                                              'Vaša grupa: ${userGroup.replaceAll('group_', 'Grupa ')}',
                                              style: const TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.w600,
                                                color: mainColor,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ),
                                          leaderboardListItem(players),
                                        ],
                                      );
                                    },
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                      Align(
                        alignment: Alignment.bottomCenter,
                        child: Container(child: leagueClockWidget()),
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget leaderboardListItem(List<LeaguePlayerModel> players) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const RankHeaderWidget(
          isOnResult: false,
        ),
        const SizedBox(height: 16),
        const Text(
          'Tabela',
          style: TextStyle(
            fontSize: 18.0,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8.0),
        DataTable(
          columns: const [
            DataColumn(label: Text('Rank')),
            DataColumn(label: Text('Ime')),
            DataColumn(label: Text('Neuroni')),
          ],
          rows: players.map((player) {
            final currentUid = profileController.userr.value.uid ?? '';
            final currentName = profileController.userr.value.uniqueName ?? '';
            final isCurrentUser = player.playerId == currentUid;
            final textColor =
                isCurrentUser ? const Color(0xff7CE099) : Colors.black;

            return DataRow(
              cells: [
                DataCell(Text(
                  '#${players.indexOf(player) + 1}',
                  style: TextStyle(color: textColor),
                )),
                DataCell(Text(
                  isCurrentUser ? currentName : (player.username ?? 'N/A'),
                  style: TextStyle(color: textColor),
                )),
                DataCell(Text(
                  player.score?.toString() ?? '0',
                  style: TextStyle(color: textColor),
                )),
              ],
            );
          }).toList(),
        ),
        Container(height: 150, color: Colors.transparent),
      ],
    );
  }
}
