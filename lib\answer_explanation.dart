// import 'package:bibl/controllers/profile_controller.dart';
// import 'package:bibl/models/question_model.dart';
// import 'package:bibl/models/quiz_model.dart';
// import 'package:bibl/res/style.dart';
// import 'package:bibl/widgets/custombutton.dart';
// import 'package:bibl/widgets/question_widget.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:get/get.dart';

// import 'controllers/analytics_controller.dart';
// import 'widgets/arrow_back_button_widget.dart';
// import 'widgets/customappbar.dart';

// class AnswerExplanation extends StatefulWidget {
//   final QuizModel quiz;
//   final QuestionModel question;
//   final List<String?> selectedAnswers;
//   const AnswerExplanation(
//       {super.key,
//       required this.question,
//       required this.quiz,
//       required this.selectedAnswers});

//   @override
//   State<AnswerExplanation> createState() => _AnswerExplanationState();
// }

// class _AnswerExplanationState extends State<AnswerExplanation> {
//   ProfileController profileController = Get.find();
//   final PageController _pageController = PageController();
//   final AnalticsController analticsController = AnalticsController();
//   int currentPage = 0;

//   @override
//   void initState() {
//     super.initState();

//     // currentPage = widget.question.qsNo!;
//     // ignore: unused_local_variable
//     // for (var qs in widget.quiz.questionsList!) {
//     //   selectedOptions.add(null);
//     //   selectedOptionsIndexes.add(null);
//     // }
//   }

//   @override
//   void dispose() {
//     _pageController.dispose();
//     super.dispose();
//   }

//   bool _isGoingBack = false;
//   void _goToPreviousPage() {
//     if (_isGoingBack) return;

//     if (currentPage > 0) {
//       _pageController.previousPage(
//         duration: const Duration(milliseconds: 500),
//         curve: Curves.easeInOut,
//       );
//     } else {
//       Future.delayed(Duration.zero, () {
//         _isGoingBack = true;
//         Get.back();
//       });
//     }
//   }

//   void _goToNextPage() {
//     if (currentPage < widget.quiz.questionsList!.length - 1) {
//       _pageController.nextPage(
//         duration: const Duration(milliseconds: 500),
//         curve: Curves.easeInOut,
//       );
//     } else {
//       Get.back();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     final question = widget.quiz.questionsList![currentPage];

//     return SafeArea(
//         bottom: true,
//         top: false,
//         child: Scaffold(
//           appBar: CustomAppBar(
//             title: 'Objašnjenje Odgovora',
//           ),
//           body: Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: PageView.builder(
//               physics: const NeverScrollableScrollPhysics(),
//               controller: _pageController,
//               onPageChanged: (page) {
//                 setState(() {
//                   currentPage = page;
//                 });
//               },
//               itemCount: widget.quiz.questionsList!.length,
//               itemBuilder: (context, index) {
//                 return Stack(
//                   children: [
//                     ScrollConfiguration(
//                       behavior: const ScrollBehavior(),
//                       child: ListView(
//                         children: [
//                           Container(
//                             padding: const EdgeInsets.all(16.0),
//                             decoration: BoxDecoration(
//                                 border: Border.all(
//                                     color: Colors.black.withValues(alpha: 0.2)),
//                                 borderRadius: BorderRadius.circular(20)),
//                             child: ListView(
//                               physics: const NeverScrollableScrollPhysics(),
//                               shrinkWrap: true,
//                               padding: const EdgeInsets.all(0),
//                               children: [
//                                 AnswersPageQuestionWidget(
//                                   selectedAnswers: widget.selectedAnswers,
//                                   totalQuestions:
//                                       widget.quiz.questionsList!.length,
//                                   question: question,
//                                 ),
//                               ],
//                             ),
//                           ),
//                           const SizedBox(
//                             height: 150,
//                           )
//                         ],
//                       ),
//                     ),
//                     Align(
//                       alignment: Alignment.bottomCenter,
//                       child: Row(
//                         children: [
//                           Expanded(
//                             child: buttonContainer(
//                               text: 'Prethodno',
//                               onTap: _goToPreviousPage,
//                             ),
//                           ),
//                           const SizedBox(
//                             width: 5,
//                           ),
//                           Expanded(
//                             child: buttonContainer(
//                               text: 'Potvrdi',
//                               onTap: _goToNextPage,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 );
//               },
//             ),
//           ),
//         ));
//   }

//   CustomAppBar quizAppbarWidget() {
//     return CustomAppBar(
//       widget: Row(
//         children: [
//           const SizedBox(
//             width: 10,
//           ),
//           arrowBackButtonWidget(),
//           const SizedBox(
//             width: 10,
//           ),
//           Expanded(
//             child: LinearProgressIndicator(
//               borderRadius: BorderRadius.circular(16),
//               minHeight: 6,
//               value: (currentPage + 1) / widget.quiz.questionsList!.length,
//               backgroundColor: Colors.white.withValues(alpha: 0.2),
//               valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
//             ),
//           ),
//           const SizedBox(
//             width: 10,
//           ),
//           Container(
//             width: 60,
//             height: 28,
//             decoration: BoxDecoration(
//                 color: Colors.white.withValues(alpha: 0.2),
//                 border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
//                 borderRadius: BorderRadius.circular(60)),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 SvgPicture.asset('assets/svgs/cup_icon.svg'),
//                 const SizedBox(
//                   width: 5,
//                 ),
//                 const Txt(
//                   txt: '10+',
//                   fontSize: 12,
//                   fontWeight: FontWeight.w600,
//                   fontColor: Colors.white,
//                 )
//               ],
//             ),
//           ),
//           const SizedBox(
//             width: 30,
//           ),
//         ],
//       ),
//     );
//   }
// }

// class ShuffleQuizAnswersExplanation extends StatefulWidget {
//   final ShuffleQuizModel quiz;
//   final ShuffleQuizQuestionModel question;
//   final List<bool?> selectedAnswers;
//   const ShuffleQuizAnswersExplanation(
//       {super.key,
//       required this.quiz,
//       required this.question,
//       required this.selectedAnswers});

//   @override
//   State<ShuffleQuizAnswersExplanation> createState() =>
//       _ShuffleQuizAnswersExplanationState();
// }

// class _ShuffleQuizAnswersExplanationState
//     extends State<ShuffleQuizAnswersExplanation> {
//   ProfileController profileController = Get.find();
//   final PageController _pageController = PageController();
//   final AnalticsController analticsController = AnalticsController();
//   int currentPage = 0;

//   @override
//   void dispose() {
//     _pageController.dispose();
//     super.dispose();
//   }

//   bool _isGoingBack = false;
//   void _goToPreviousPage() {
//     if (_isGoingBack) return;

//     if (currentPage > 0) {
//       _pageController.previousPage(
//         duration: const Duration(milliseconds: 500),
//         curve: Curves.easeInOut,
//       );
//     } else {
//       Future.delayed(Duration.zero, () {
//         _isGoingBack = true;
//         Get.back();
//       });
//     }
//   }

//   void _goToNextPage() {
//     if (currentPage < widget.quiz.questionsList!.length - 1) {
//       _pageController.nextPage(
//         duration: const Duration(milliseconds: 500),
//         curve: Curves.easeInOut,
//       );
//     } else {
//       Get.back();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     final question = widget.quiz.questionsList![currentPage];

//     return SafeArea(
//         bottom: true,
//         top: false,
//         child: Scaffold(
//           appBar: CustomAppBar(
//             title: 'Objašnjenje Odgovora',
//           ),
//           body: Padding(
//             padding: const EdgeInsets.all(16.0),
//             child: PageView.builder(
//               physics: const NeverScrollableScrollPhysics(),
//               controller: _pageController,
//               onPageChanged: (page) {
//                 setState(() {
//                   currentPage = page;
//                 });
//               },
//               itemCount: widget.quiz.questionsList!.length,
//               itemBuilder: (context, index) {
//                 return Stack(
//                   children: [
//                     ListView(
//                       children: [
//                         Container(
//                           padding: const EdgeInsets.all(16.0),
//                           decoration: BoxDecoration(
//                               border: Border.all(
//                                   color: Colors.black.withValues(alpha: 0.2)),
//                               borderRadius: BorderRadius.circular(20)),
//                           child: ListView(
//                             physics: const NeverScrollableScrollPhysics(),
//                             shrinkWrap: true,
//                             padding: const EdgeInsets.all(0),
//                             children: [
//                               ShuffleQuizQuestionWidget(
//                                 selectedAnswer: widget.selectedAnswers[index],
//                                 totalQuestions:
//                                     widget.quiz.questionsList!.length,
//                                 question: question,
//                               ),
//                             ],
//                           ),
//                         ),
//                         const SizedBox(
//                           height: 150,
//                         )
//                       ],
//                     ),
//                     Align(
//                       alignment: Alignment.bottomCenter,
//                       child: Row(
//                         children: [
//                           Expanded(
//                             child: buttonContainer(
//                               text: 'Prethodno',
//                               onTap: _goToPreviousPage,
//                             ),
//                           ),
//                           const SizedBox(
//                             width: 5,
//                           ),
//                           Expanded(
//                             child: buttonContainer(
//                               text: 'Potvrdi',
//                               onTap: _goToNextPage,
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ],
//                 );
//               },
//             ),
//           ),
//         ));
//   }

//   CustomAppBar quizAppbarWidget() {
//     return CustomAppBar(
//       widget: Row(
//         children: [
//           const SizedBox(
//             width: 10,
//           ),
//           arrowBackButtonWidget(),
//           const SizedBox(
//             width: 10,
//           ),
//           Expanded(
//             child: LinearProgressIndicator(
//               borderRadius: BorderRadius.circular(16),
//               minHeight: 6,
//               value: (currentPage + 1) / widget.quiz.questionsList!.length,
//               backgroundColor: Colors.white.withValues(alpha: 0.2),
//               valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
//             ),
//           ),
//           const SizedBox(
//             width: 10,
//           ),
//           Container(
//             width: 60,
//             height: 28,
//             decoration: BoxDecoration(
//                 color: Colors.white.withValues(alpha: 0.2),
//                 border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
//                 borderRadius: BorderRadius.circular(60)),
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 SvgPicture.asset('assets/svgs/cup_icon.svg'),
//                 const SizedBox(
//                   width: 5,
//                 ),
//                 const Txt(
//                   txt: '10+',
//                   fontSize: 12,
//                   fontWeight: FontWeight.w600,
//                   fontColor: Colors.white,
//                 )
//               ],
//             ),
//           ),
//           const SizedBox(
//             width: 30,
//           ),
//         ],
//       ),
//     );
//   }
// }
