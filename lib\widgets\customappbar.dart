import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../res/style.dart';
import 'arrow_back_button_widget.dart';

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  @override
  final Size preferredSize;
  final Widget? widget;
  final bool? isBackButton;
  final String? title;
  final double height;

  CustomAppBar({
    Key? key,
    this.widget,
    this.title,
    this.isBackButton,
    this.height = 150.0, // Default height is 150
  })  : preferredSize = Size.fromHeight(height),
        super(key: key);

  @override
  State<CustomAppBar> createState() => _CustomAppBarState();
}

class _CustomAppBarState extends State<CustomAppBar> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height, // Use the height parameter
      decoration: BoxDecoration(
        gradient: mainColorsGradient,
        borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(18), bottomRight: Radius.circular(18)),
        boxShadow: const [
          BoxShadow(
            color: Colors.black26,
            offset: Offset(0, 4),
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        // mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(
            flex: 2,
          ),
          widget.widget ??
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  widget.isBackButton == null
                      ? Row(
                          children: [
                            SizedBox(
                              width: Get.width * 0.01,
                            ),
                            arrowBackButtonWidget(),
                          ],
                        )
                      : Row(
                          children: [
                            SizedBox(
                              width: Get.width * 0.01,
                            ),
                            const IconButton(
                                onPressed: null,
                                icon: CircleAvatar(
                                  radius: 14,
                                  backgroundColor: Colors.transparent,
                                  child: Padding(
                                    padding: EdgeInsets.all(5.0),
                                    child: Icon(
                                      Icons.arrow_back_ios_new,
                                      size: 12,
                                      color: Colors.transparent,
                                    ),
                                  ),
                                )),
                          ],
                        ),
                  Flexible(
                    child: Txt(
                      txt: widget.title ?? '',
                      fontSize: 20,
                      fontColor: Colors.white,
                    ),
                  ),
                  Row(
                    children: [
                      const IconButton(
                          onPressed: null,
                          icon: CircleAvatar(
                            radius: 14,
                            backgroundColor: Colors.transparent,
                            child: Padding(
                              padding: EdgeInsets.all(5.0),
                              child: Icon(
                                Icons.arrow_back_ios_new,
                                size: 12,
                                color: Colors.transparent,
                              ),
                            ),
                          )),
                      SizedBox(
                        width: Get.width * 0.01,
                      ),
                    ],
                  ),
                ],
              ),
          const Spacer(),
        ],
      ),
    );
  }
}
