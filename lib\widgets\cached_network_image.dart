import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:shimmer/shimmer.dart';

CachedNetworkImage cachedNetworkImage(
  String image, {
  bool forceRefresh = false,
}) {
  return CachedNetworkImage(
    imageUrl: image,
    cacheManager: CacheManager(Config(
      'images_cache',
      stalePeriod: const Duration(days: 7), // Adjust as needed
      maxNrOfCacheObjects: 1000, // Increase the cache size
    )),
    // Force refresh if needed (useful when categories change)
    cacheKey: forceRefresh
        ? '${image}_${DateTime.now().millisecondsSinceEpoch}'
        : null,
    errorWidget: (context, url, error) => Shimmer.fromColors(
        baseColor: Colors.grey.withValues(alpha: 0.1),
        highlightColor: Colors.white,
        child: Container(
          color: Colors.white,
        )),
    progressIndicatorBuilder: (context, url, downloadProgress) =>
        Shimmer.fromColors(
            baseColor: Colors.grey.withValues(alpha: 0.1),
            highlightColor: Colors.white,
            child: Container(
              color: Colors.white,
            )),
    imageBuilder: (context, imageProvider) => Container(
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.all(
          Radius.circular(14),
        ),
        image: DecorationImage(
          image: imageProvider,
          fit: BoxFit.cover,
        ),
      ),
    ),
  );
}
