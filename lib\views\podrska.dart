import 'package:bibl/widgets/customappbar.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class <PERSON>drska extends StatefulWidget {
  const Podrska({super.key});

  @override
  State<Podrska> createState() => _PodrskaState();
}

class _PodrskaState extends State<Podrska> {
  final Email email = Email(
    recipients: ['<EMAIL>'],
    isHTML: false,
  );

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        body: Column(children: [
          CustomAppBar(
            widget: null,
            title: '<PERSON><PERSON><PERSON><PERSON>',
          ),
          Expanded(
            child: Stack(
              children: [
                ScrollConfiguration(
                  behavior: const ScrollBehavior(),
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              SizedBox(
                                height: Get.height * 0.03,
                              ),
                              SvgPicture.asset(
                                  'assets/svgs/podrska_illustration.svg'),
                              SizedBox(
                                height: Get.height * 0.1,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: buttonContainer(
                      onTap: () async {
                        await FlutterEmailSender.send(email);
                      },
                      text: 'Pošaljite nam Email',
                    ),
                  ),
                )
              ],
            ),
          ),
        ]),
      ),
    );
  }
}
