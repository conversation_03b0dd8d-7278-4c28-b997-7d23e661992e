import 'dart:io';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:path_provider/path_provider.dart';

import '../models/lesson_model.dart';
import '../services/shared_preferences.dart';

class AudioController extends GetxController {
  final AudioPlayer player = AudioPlayer();
  String? currentAudioPath;
  List<Future> fetchAudioFutures = [];

  @override
  void onInit() {
    super.onInit();

    download();
  }

  download() async {
    final lastSyncTime = SharedPrefs.getData(key: 'lastSyncTime') ?? 0;
    Query query = firestore
        .collection('lessons')
        .where(
          'lastUpdated',
          isGreaterThan: Timestamp.fromMillisecondsSinceEpoch(lastSyncTime),
        )
        .orderBy('lastUpdated', descending: false);

    final snapshot = await query.get();
    for (var doc in snapshot.docs) {
      try {
        final lesson = LessonModel.fromJson(doc.data() as Map<String, dynamic>);
        final localPath =
            await _getLocalFilePath("${lesson.lessonId}_audio.mp3");

        // Check if file already not exists locally
        if (!File(localPath).existsSync()) {
          fetchAudioFutures.add(_downloadAndSaveAudio(
              lesson.audioLink!, "${lesson.lessonId}_audio.mp3"));
        }
      } catch (e) {
        //
      }
    }
    await Future.wait(fetchAudioFutures);
  }

  // Preload audio: Checks local storage or downloads and stores it
  Future<bool> preloadAudio(String url, String fileName) async {
    final ProfileController profileController = Get.find();
    try {
      final localPath = await _getLocalFilePath(fileName);

      // Check if file already exists locally
      if (File(localPath).existsSync()) {
        await player.setFilePath(localPath);
        currentAudioPath = localPath;
      } else {
        await _downloadAndSaveAudio(url, fileName);
        await player.setFilePath(localPath);
        currentAudioPath = localPath;
      }

      if (profileController.isSoundOn.value) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // Play audio
  Future<void> playAudio() async {
    try {
      await player.play();
    } catch (e) {
      //
    }
  }

  // Stop audio
  Future<void> stopAudio() async {
    try {
      if (player.playing) {
        await player.stop();
      }
    } catch (e) {
      //
    }
  }

  // Get the local file path for caching
  Future<String> _getLocalFilePath(String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/$fileName';
  }

  // Download and save the audio file locally
  Future<void> _downloadAndSaveAudio(String url, String fileName) async {
    try {
      final file = await DefaultCacheManager().getSingleFile(url);
      final localPath = await _getLocalFilePath(fileName);
      await file.copy(localPath);
    } catch (e) {
      //
    }
  }

  @override
  void onClose() {
    player.dispose();
    super.onClose();
  }
}
