import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import 'cross_widget.dart';
import 'dialog_widget.dart';

class FeedbackDialog extends StatefulWidget {
  const FeedbackDialog({super.key});

  @override
  State<FeedbackDialog> createState() => _FeedbackDialogState();
}

class _FeedbackDialogState extends State<FeedbackDialog> {
  final Email email = Email(
    recipients: ['<EMAIL>'],
    isHTML: false,
  );

  @override
  Widget build(BuildContext context) {
    return dialogWidget(
      Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Close Button
          crossWidget(),

          SvgPicture.asset(
            'assets/svgs/feedback.svg',
            height: 150,
          ),

          const SizedBox(height: 20),

          const Txt(
            txt: '<PERSON><PERSON> nam je da je tako.',
            font: 'Inter',
            fontSize: 24,
            fontWeight: FontWeight.w600,
          ),

          const SizedBox(height: 10),
          const Txt(
            txt:
                'Molimo Vas recite nam šta da promenimo, kako bi smo bili bolji?',
            fontSize: 14,
            fontWeight: FontWeight.normal,
            maxLines: 3,
            fontColor: grey2Color,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 20),

          // Thumbs Up and Down Buttons
          buttonContainer(
            text: 'Send us feedback',
            onTap: () async {
              Get.back();
              await FlutterEmailSender.send(email);
            },
          )
        ],
      ),
    );
  }
}
