import 'package:flutter/material.dart';
import '../res/style.dart';

Widget leagueClockWidget() {
  final now = DateTime.now();

  // Calculate start of the current week (Monday 00:00)
  DateTime startOfWeek = now.subtract(Duration(days: now.weekday - 1));
  startOfWeek = DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day);

  DateTime endOfWeek =
      startOfWeek.add(const Duration(days: 6, hours: 23, minutes: 59));

  // Adjust to next week if current time is after endOfWeek
  if (now.isAfter(endOfWeek)) {
    startOfWeek = startOfWeek.add(const Duration(days: 7));
    endOfWeek =
        startOfWeek.add(const Duration(days: 6, hours: 23, minutes: 59));
  }

  final remaining = endOfWeek.difference(now);
  final daysRemaining = remaining.inDays;
  final hoursRemaining = remaining.inHours % 24;

  // Serbian day text
  String dayText;
  if (daysRemaining == 1) {
    dayText = '1 dan';
  } else {
    dayText = '$daysRemaining dana';
  }

  // Serbian hour text according to specific rules
  String hourText;
  if (hoursRemaining == 1 || hoursRemaining == 21) {
    hourText = '$hoursRemaining sat';
  } else if ([2, 3, 4, 22, 23, 24].contains(hoursRemaining)) {
    hourText = '$hoursRemaining sata';
  } else {
    hourText = '$hoursRemaining sati';
  }

  // Final text
  String displayText = dayText;
  if (hoursRemaining > 0) {
    displayText += ' - $hourText';
  }

  return Padding(
    padding: const EdgeInsets.all(16.0),
    child: Container(
      height: 86,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: const Color(0xFFFFEFEF),
        borderRadius: BorderRadius.circular(16.0),
        border: Border.all(color: Colors.orange),
      ),
      child: Row(
        children: [
          Image.asset('assets/images/clock.png'),
          const SizedBox(width: 16.0),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Txt(
                txt: 'Liga traje još:',
                fontSize: 14,
                fontColor: grey2Color,
              ),
              Txt(
                txt: displayText,
                fontSize: 16,
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
