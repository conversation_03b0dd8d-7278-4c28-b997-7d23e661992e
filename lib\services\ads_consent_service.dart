import 'dart:async';

import 'package:google_mobile_ads/google_mobile_ads.dart';

class AdsConsentService {
  Future<FormError?> initialize() async {
    // try {
    //   await ConsentInformation.instance.reset();
    //   print('helloreset done');
    // } catch (e) {
    //   print('helloerror:$e');
    // }

    final completer = Completer<FormError?>();

    final params = ConsentRequestParameters(
        // consentDebugSettings: ConsentDebugSettings(
        //     debugGeography: DebugGeography.debugGeographyEea,
        //     testIdentifiers: ["421C6F24A6902C0E0698E0BF943021C5"]
        //     )
        );

    ConsentInformation.instance.requestConsentInfoUpdate(params, () async {
      if (await ConsentInformation.instance.isConsentFormAvailable()) {
        await _loadConsentForm();
      } else {
        await _initialize();
      }

      completer.complete();
    }, (error) {
      completer.complete(error);
    });

    return completer.future;
  }

  Future<FormError?> _loadConsentForm() async {
    final completer = Completer<FormError?>();

    ConsentForm.loadConsentForm((consentForm) async {
      final status = await ConsentInformation.instance.getConsentStatus();
      if (status == ConsentStatus.required) {
        consentForm.show((formError) {
          completer.complete(_loadConsentForm());
        });
      } else {
        await _initialize();
        completer.complete();
      }
    }, (FormError? error) {
      completer.complete(error);
    });

    return completer.future;
  }

  Future<void> _initialize() async {
    await MobileAds.instance.initialize();
  }
}
