import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';

import '../controllers/auth_controller.dart';
import '../models/lesson_model.dart';
import '../models/quiz_model.dart';
import '../models/question_model.dart';
import '../widgets/box_widget.dart';
import '../widgets/customappbar.dart'; // Ensure you have this widget implemented

class ItemLinkLandingPage extends StatefulWidget {
  final String type;
  final String itemId;

  const ItemLinkLandingPage(
      {super.key, required this.type, required this.itemId});

  @override
  State<ItemLinkLandingPage> createState() => _ItemLinkLandingPageState();
}

class _ItemLinkLandingPageState extends State<ItemLinkLandingPage> {
  late Stream<DocumentSnapshot> _dataStream;
  final ProfileController profileController = Get.find();
  final AuthController authController = Get.find();

  @override
  void initState() {
    super.initState();
    // Initialize the stream based on the type
    authController.isThereDeepLink.value = false;

    _dataStream = _getDataStream();
  }

  Stream<DocumentSnapshot> _getDataStream() {
    if (widget.type == 'lesson') {
      return firestore.collection('lessons').doc(widget.itemId).snapshots();
    } else if (widget.type == 'quiz') {
      return firestore.collection('quizes').doc(widget.itemId).snapshots();
    } else if (widget.type == 'shuffleQuiz') {
      return firestore
          .collection('shuffleQuizes')
          .doc(widget.itemId)
          .snapshots();
    } else {
      throw Exception('Invalid type: ${widget.type}');
    }
  }

  Future<List<QuestionModel>> _fetchQuestions(String id) async {
    try {
      final querySnapshot = await firestore
          .collection('quizes')
          .doc(id)
          .collection('questionsList')
          .orderBy('qsNo') // Assuming you use 'qsNo' for ordering
          .get();

      return querySnapshot.docs
          .map((doc) => QuestionModel.fromSnap(doc))
          .toList();
    } catch (e) {
      return [];
    }
  }

  Future<List<ShuffleQuizQuestionModel>> _fetchShuffleQuizQuestions(
      String id) async {
    try {
      final querySnapshot = await firestore
          .collection('shuffleQuizes')
          .doc(id)
          .collection('questionsList')
          .orderBy('qsNo') // Assuming you use 'qsNo' for ordering
          .get();

      return querySnapshot.docs
          .map((doc) => ShuffleQuizQuestionModel.fromSnap(doc))
          .toList();
    } catch (e) {
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: 'umniLab',
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: StreamBuilder<DocumentSnapshot>(
          stream: _dataStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }

            if (snapshot.hasError) {
              return Center(child: Text('Error: ${snapshot.error}'));
            }

            if (!snapshot.hasData || !snapshot.data!.exists) {
              return const Center(child: Text('No data found.'));
            }

            // Parse the data from Firestore
            final itemData = snapshot.data!;

            // Determine the model based on the type
            if (widget.type == 'lesson') {
              final item =
                  LessonModel.fromSnap(itemData); // Parse to LessonModel
              return BoxWidget(
                lesson: item,
              );
            } else if (widget.type == 'quiz') {
              return FutureBuilder<List<QuestionModel>>(
                future: _fetchQuestions(widget.itemId), // Fetch questions
                builder: (context, questionSnapshot) {
                  if (questionSnapshot.connectionState ==
                      ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (questionSnapshot.hasError) {
                    return Center(
                        child: Text('Error: ${questionSnapshot.error}'));
                  }

                  final questions = questionSnapshot.data ?? [];
                  final item = QuizModel.fromSnap(itemData)
                    ..questionsList = questions; // Parse to QuizModel

                  return BoxWidget(
                    quiz: item,
                  );
                },
              );
            } else if (widget.type == 'shuffleQuiz') {
              return FutureBuilder<List<ShuffleQuizQuestionModel>>(
                future: _fetchShuffleQuizQuestions(
                    widget.itemId), // Fetch questions
                builder: (context, questionSnapshot) {
                  if (questionSnapshot.connectionState ==
                      ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (questionSnapshot.hasError) {
                    return Center(
                        child: Text('Error: ${questionSnapshot.error}'));
                  }

                  final questions = questionSnapshot.data ?? [];
                  final item = ShuffleQuizModel.fromSnap(itemData)
                    ..questionsList = questions; // Parse to ShuffleQuizModel

                  return BoxWidget(
                    shuffleQuiz: item,
                  );
                },
              );
            } else {
              return const SizedBox
                  .shrink(); // In case something unexpected occurs
            }
          },
        ),
      ),
    );
  }
}
