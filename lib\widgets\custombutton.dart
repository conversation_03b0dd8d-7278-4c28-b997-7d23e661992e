import 'package:bibl/res/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

Widget buttonContainer(
    {double? width,
    double? height,
    Color? fontColor,
    bool? isOnlyBorder,
    String? text,
    double? fontSize,
    Widget? child,
    bool? isArrow,
    bool? isForOnboarding,
    FontWeight? fontWeight,
    VoidCallback? onTap}) {
  return onTap != null
      ? SizedBox(
          height: height ?? 58,
          child: ElevatedButton(
              onPressed: onTap,
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets
                    .zero, // Removes padding to ensure gradient fills entire button
                shape: RoundedRectangleBorder(
                  side: isOnlyBorder != null
                      ? const BorderSide(color: mainColor, width: 2)
                      : BorderSide.none,
                  borderRadius: BorderRadius.circular(50.0),
                ),
              ),
              child: Ink(
                  decoration: BoxDecoration(
                    gradient: isForOnboarding != null
                        ? mainColorsGradient2
                        : isOnlyBorder != null
                            ? null
                            : mainColorsGradient,
                    borderRadius: BorderRadius.circular(50.0),
                  ),
                  child: Container(
                    alignment: Alignment.center,
                    child: child ??
                        (isArrow != null
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  ButtonTxt(
                                    txt: text!,
                                    isOnlyBorder: isOnlyBorder,
                                  ),
                                  RotatedBox(
                                      quarterTurns: 2,
                                      child: Icon(
                                        Icons.arrow_back_ios_new,
                                        color: isOnlyBorder != null
                                            ? mainColor
                                            : Colors.black,
                                        size: 16,
                                      ))
                                ],
                              )
                            : Center(
                                child: ButtonTxt(
                                txt: text!,
                                fontSize: fontSize,
                              ))),
                  ))))
      : SizedBox(
          height: 58,
          child: ElevatedButton(
              onPressed: onTap,
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets
                    .zero, // Removes padding to ensure gradient fills entire button
                shape: RoundedRectangleBorder(
                  side: isOnlyBorder != null
                      ? const BorderSide(color: mainColor, width: 2)
                      : BorderSide.none,
                  borderRadius: BorderRadius.circular(50.0),
                ),
              ),
              child: Container(
                alignment: Alignment.center,
                child: child ??
                    (isArrow != null
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              ButtonTxt(
                                txt: text!,
                                isOnlyBorder: isOnlyBorder,
                              ),
                              RotatedBox(
                                  quarterTurns: 2,
                                  child: Icon(
                                    Icons.arrow_back_ios_new,
                                    color: isOnlyBorder != null
                                        ? mainColor
                                        : Colors.black,
                                    size: 16,
                                  ))
                            ],
                          )
                        : Center(
                            child: ButtonTxt(
                            txt: text!,
                            fontSize: fontSize,
                          ))),
              )));
}

Widget outlineButtonContainer(
    {double? width,
    Color? fontColor,
    double? height,
    bool? isOnlyBorder,
    String? text,
    double? fontSize,
    Widget? child,
    bool? isArrow,
    FontWeight? fontWeight,
    VoidCallback? onTap}) {
  return SizedBox(
      height: height ?? 58,
      child: OutlinedButton(
        onPressed: onTap,
        style: ButtonStyle(
          side: WidgetStateProperty.all<BorderSide>(
            const BorderSide(color: mainColor, width: 2),
          ),
          shape: WidgetStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(50),
            ),
          ),
        ),
        child: child ??
            (isArrow != null
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ButtonTxt(
                        txt: text!,
                        isOnlyBorder: isOnlyBorder,
                      ),
                      RotatedBox(
                          quarterTurns: 2,
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            color:
                                isOnlyBorder != null ? mainColor : Colors.black,
                            size: 16,
                          ))
                    ],
                  )
                : Center(
                    child: ButtonTxt(
                    txt: text!,
                  ))),
      ));
}

Widget outlineButtonContainer2({
  double? width,
  Color? fontColor,
  bool? isOnlyBorder,
  String? text,
  double? fontSize,
  Widget? child,
  bool? isArrow,
  FontWeight? fontWeight,
  VoidCallback? onTap,
}) {
  return Container(
    height: 78,
    width: width, // Optional width if provided
    decoration: BoxDecoration(
      gradient: mainColorsGradient,
      borderRadius: BorderRadius.circular(50),
    ),
    child: OutlinedButton(
      onPressed: onTap,
      style: ButtonStyle(
        backgroundColor: WidgetStateProperty.all(
            Colors.transparent), // Ensure the button is transparent

        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(50),
          ),
        ),
      ),
      child: child,
    ),
  );
}

Widget authButton(
    {double? width,
    required String text,
    double? fontSize,
    FontWeight? fontWeight,
    VoidCallback? onTap}) {
  return SizedBox(
      height: 50, // Adjust the height as needed
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: mainColor,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(14.0)),
        ),
        child: text == 'apple'
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                      width: 30, child: Image.asset('assets/images/apple.png')),
                  SizedBox(
                    width: Get.width * 0.02,
                  ),
                  const Flexible(
                    child: Txt(
                        txt: 'Nastavite sa Apple nalogom',
                        fontSize: 16,
                        minFontSize: 16,
                        fontWeight: FontWeight.w700),
                  ),
                ],
              )
            : text == 'google'
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 30,
                        child: Image.asset('assets/images/google.png'),
                      ),
                      SizedBox(
                        width: Get.width * 0.02,
                      ),
                      const Flexible(
                        child: Txt(
                            txt: 'Nastavite sa Google nalogom',
                            minFontSize: 16,
                            fontSize: 16,
                            fontWeight: FontWeight.w700),
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 30,
                        child: Image.asset('assets/images/message.png'),
                      ),
                      SizedBox(
                        width: Get.width * 0.02,
                      ),
                      const Flexible(
                        child: Txt(
                            txt: 'Nastavite sa Email nalogom',
                            fontSize: 16,
                            minFontSize: 16,
                            fontWeight: FontWeight.w700),
                      ),
                    ],
                  ),
      ));
}
