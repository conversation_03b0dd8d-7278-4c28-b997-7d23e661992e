import 'dart:async';
// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as p;
import 'package:sqflite/sqflite.dart';
import '../models/lesson_model.dart';
import '../models/page_model.dart';
import '../models/question_model.dart';
import '../models/quiz_model.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;

  static Database? _database;

  DatabaseHelper._internal();

  /// Get database instance
  Future<Database> get database async {
    try {
      if (_database != null) return _database!;
      _database = await _initDatabase();
      return _database!;
    } catch (e) {
      //
      rethrow;
    }
  }

  /// Initialize SQLite database
  Future<Database> _initDatabase() async {
    try {
      final dbPath = await getDatabasesPath();
      final path = p.join(dbPath, 'app_database.db');

      return await openDatabase(
        path,
        version: 1,
        onCreate: (db, version) async {
          // Create lessons table
          await db.execute('''
          CREATE TABLE lessons (
            lessonId TEXT PRIMARY KEY NOT NULL,
            lessonName TEXT,
            lessonNo INTEGER,
            intro TEXT,
            audioLink TEXT,
            imageLink TEXT,
            category TEXT
          );
        ''');

          // Create pages table
          await db.execute('''
          CREATE TABLE pages (
            lessonId TEXT NOT NULL,
            pageNo INTEGER NOT NULL,
            pageTitle TEXT,
            pagePhotoLink TEXT,
            pageContent TEXT,
            PRIMARY KEY(lessonId, pageNo),
            FOREIGN KEY(lessonId) REFERENCES lessons(lessonId) ON DELETE CASCADE
          );
        ''');

          // Create table for pages questions
          await db.execute('''
          CREATE TABLE pagesquestions (
            lessonId TEXT NOT NULL,
            pageNo INTEGER NOT NULL,
            qsNo INTEGER NOT NULL,
            question TEXT,
            correctOption TEXT,
            options TEXT,
            PRIMARY KEY(lessonId, pageNo, qsNo),
            FOREIGN KEY(lessonId, pageNo) REFERENCES pages(lessonId, pageNo) ON DELETE CASCADE
          );
        ''');

          // Create quizzes table
          await db.execute('''
          CREATE TABLE quizzes (
            quizId TEXT PRIMARY KEY NOT NULL,
            quizName TEXT,
            category TEXT,
            quizImageLink TEXT,
            quizNo INTEGER
          );
        ''');

          // Create shuffle quizzes table
          await db.execute('''
          CREATE TABLE shuffle_quizzes (
            quizId TEXT PRIMARY KEY NOT NULL,
            quizName TEXT,
            category TEXT,
            quizImageLink TEXT,
            quizNo INTEGER
          );
        ''');

          // Create table for quiz questions
          await db.execute('''
          CREATE TABLE quizquestions (
            quizId TEXT NOT NULL,
            qsNo INTEGER NOT NULL,
            question TEXT,
            correctOption TEXT,
            options TEXT,
            PRIMARY KEY(quizId, qsNo),
            FOREIGN KEY(quizId) REFERENCES quizzes(quizId) ON DELETE CASCADE
          );
        ''');

          // Create table for shuffle quiz questions
          await db.execute('''
          CREATE TABLE shufflequizquestions (
            quizId TEXT NOT NULL,
            qsNo INTEGER NOT NULL,
            question TEXT,
             correctOption INTEGER NOT NULL, -- BOOLEAN stored as 0 (false) or 1 (true)
            qsImage TEXT,
            PRIMARY KEY(quizId, qsNo),
            FOREIGN KEY(quizId) REFERENCES shuffle_quizzes(quizId) ON DELETE CASCADE
          );
        ''');

          // Add Indexes
          await db.execute(
              'CREATE INDEX idx_lessons_lessonId ON lessons(lessonId);');
          await db.execute(
              'CREATE INDEX idx_pages_lessonId_pageNo ON pages(lessonId, pageNo);');
          await db.execute(
              'CREATE INDEX idx_pagesquestions_lessonId_pageNo ON pagesquestions(lessonId, pageNo);');
          await db.execute(
              'CREATE INDEX idx_quizquestions_quizId ON quizquestions(quizId);');
          await db.execute(
              'CREATE INDEX idx_shufflequizquestions_quizId ON shufflequizquestions(quizId);');
        },
      );
    } catch (e) {
      rethrow;
    }
  }

  /// Insert a lesson with associated pages and questions
  Future<void> insertLesson(LessonModel lesson) async {
    try {
      final db = await database;

      // Insert the lesson
      await db.insert(
        'lessons',
        lesson.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      // Insert associated pages
      if (lesson.pages != null && lesson.pages!.isNotEmpty) {
        for (final page in lesson.pages!) {
          await db.insert(
            'pages',
            page.toMap(lesson.lessonId!),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );

          // Insert associated questions for the page
          if (page.quiz != null) {
            await db.insert(
              'pagesquestions',
              {
                'lessonId': lesson.lessonId,
                'pageNo': page.pageNo ?? 0,
                ...page.quiz!.toMap(), // Merge quiz data
              },
              conflictAlgorithm: ConflictAlgorithm.replace,
            );
          }
        }
      }
    } catch (e) {
      //
    }
  }

  Future<List<LessonModel>> getLessons() async {
    try {
      final db = await database;

      final rawData = await db.rawQuery('''
      SELECT 
        l.lessonId, l.lessonName, l.lessonNo, l.intro, l.audioLink, l.imageLink, l.category,
        p.pageNo, p.pageTitle, p.pagePhotoLink, p.pageContent,
        q.qsNo, q.question, q.correctOption, q.options
      FROM lessons AS l
      LEFT JOIN pages AS p ON l.lessonId = p.lessonId
      LEFT JOIN pagesquestions AS q ON l.lessonId = q.lessonId AND p.pageNo = q.pageNo
      ORDER BY l.lessonNo, p.pageNo, q.qsNo;
    ''');

      final Map<String, LessonModel> lessonMap = {};

      for (final row in rawData) {
        final lessonId = row['lessonId'] as String;

        // Add lesson if not already in the map
        if (!lessonMap.containsKey(lessonId)) {
          lessonMap[lessonId] = LessonModel(
            lessonId: lessonId,
            lessonName: row['lessonName'] as String?,
            lessonNo: row['lessonNo'] as int?,
            intro: row['intro'] as String?,
            audioLink: row['audioLink'] as String?,
            imageLink: row['imageLink'] as String?,
            category: row['category'] as String?,
            pages: [],
          );
        }

        // Process page data
        if (row['pageNo'] != null) {
          final pageNo = row['pageNo'] as int;
          final pages = lessonMap[lessonId]!.pages!;

          // Check if the page already exists
          PageModel? page = pages.firstWhere(
            (p) => p.pageNo == pageNo,
            orElse: () {
              final newPage = PageModel(
                pageNo: pageNo,
                pageTitle: row['pageTitle'] as String?,
                pagePhotoLink: row['pagePhotoLink'] as String?,
                pageContent: row['pageContent'] as String?,
                quiz: null,
              );
              pages.add(newPage); // Add the new page to the list

              return newPage;
            },
          );

          // Process quiz data
          if (row['qsNo'] != null) {
            page.quiz = QuestionModel(
              qsNo: row['qsNo'] as int?,
              question: row['question'] as String?,
              correctOption: row['correctOption'] as String?,
              options: (row['options'] as String?)?.split(','),
            );
          }
        }
      }

      return lessonMap.values.toList();
    } catch (e) {
      return [];
    }
  }

  /// Insert a quiz with associated questions
  Future<void> insertQuiz(QuizModel quiz) async {
    final db = await database;

    // Insert quiz
    await db.insert(
      'quizzes',
      quiz.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // Insert associated questions
    if (quiz.questionsList != null) {
      for (var question in quiz.questionsList!) {
        await db.insert(
          'quizquestions',
          {
            'quizId': quiz.quizId,
            ...question.toMap(),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }
  }

  /// Retrieve all quizzes with their associated questions
  Future<List<QuizModel>> getQuizzes() async {
    final db = await database;

    final rawData = await db.rawQuery('''
      SELECT 
        q.quizId, q.quizName, q.category, q.quizImageLink, q.quizNo,
        qs.qsNo, qs.question, qs.correctOption, qs.options
      FROM quizzes AS q
      LEFT JOIN quizquestions AS qs ON q.quizId = qs.quizId
      ORDER BY q.quizNo, qs.qsNo;
    ''');

    Map<String, QuizModel> quizMap = {};

    for (var row in rawData) {
      final quizId = row['quizId'] as String;

      // Add quiz if not already in the map
      if (!quizMap.containsKey(quizId)) {
        quizMap[quizId] = QuizModel(
          quizId: quizId,
          quizName: row['quizName'] as String?,
          category: row['category'] as String?,
          quizImageLink: row['quizImageLink'] as String?,
          quizNo: row['quizNo'] as int?,
          questionsList: [],
        );
      }

      // Add question to the quiz
      if (row['qsNo'] != null) {
        quizMap[quizId]!.questionsList!.add(QuestionModel(
              qsNo: row['qsNo'] as int?,
              question: row['question'] as String?,
              correctOption: row['correctOption'] as String?,
              options: (row['options'] as String?)?.split(','),
            ));
      }
    }

    return quizMap.values.toList();
  }

  /// Insert a shuffle quiz
  Future<void> insertShuffleQuiz(ShuffleQuizModel shuffleQuiz) async {
    final db = await database;

    // Insert shuffle quiz
    await db.insert(
      'shuffle_quizzes',
      shuffleQuiz.toMap(),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    // Insert associated questions (if any)
    if (shuffleQuiz.questionsList != null) {
      for (var question in shuffleQuiz.questionsList!) {
        await db.insert(
          'shufflequizquestions',
          {
            'quizId': shuffleQuiz.quizId,
            ...question.toMap(),
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }
  }

  /// Retrieve all shuffle quizzes
  Future<List<ShuffleQuizModel>> getShuffleQuizzes() async {
    final db = await database;

    final rawData = await db.rawQuery('''
      SELECT 
        sq.quizId, sq.quizName, sq.category, sq.quizImageLink, sq.quizNo,
        qs.qsNo, qs.question, qs.correctOption, qs.qsImage
      FROM shuffle_quizzes AS sq
      LEFT JOIN shufflequizquestions AS qs ON sq.quizId = qs.quizId
      ORDER BY sq.quizNo, qs.qsNo;
    ''');

    Map<String, ShuffleQuizModel> shuffleQuizMap = {};

    for (var row in rawData) {
      final quizId = row['quizId'] as String;

      // Add shuffle quiz if not already in the map
      if (!shuffleQuizMap.containsKey(quizId)) {
        shuffleQuizMap[quizId] = ShuffleQuizModel(
          quizId: quizId,
          quizName: row['quizName'] as String?,
          category: row['category'] as String?,
          quizImageLink: row['quizImageLink'] as String?,
          quizNo: row['quizNo'] as int?,
          questionsList: [],
        );
      }

      // Add question to the shuffle quiz
      if (row['qsNo'] != null) {
        shuffleQuizMap[quizId]!.questionsList!.add(ShuffleQuizQuestionModel(
              qsNo: row['qsNo'] as int?,
              question: row['question'] as String?,
              correctOption: (row['correctOption'] as int) == 1,
              qsImage: row['qsImage'] as String?,
            ));
      }
    }

    return shuffleQuizMap.values.toList();
  }
}
