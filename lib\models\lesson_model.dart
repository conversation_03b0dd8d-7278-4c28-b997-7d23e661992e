import 'package:bibl/models/page_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class LessonModel {
  String? lessonName;
  String? lessonId;
  int? lessonNo;
  Timestamp? lastUpdated;
  String? intro;
  String? audioLink;
  String? imageLink;
  List<PageModel>? pages;
  Timestamp? timestamp;
  String? category;

  LessonModel({
    this.lessonName,
    this.lessonId,
    this.lessonNo,
    this.intro,
    this.lastUpdated,
    this.audioLink,
    this.imageLink,
    this.pages,
    this.timestamp,
    this.category,
  });

  // From JSON
  factory LessonModel.fromJson(Map<String, dynamic> json) {
    List<dynamic>? pagesJson = json['pages'];
    List<PageModel>? pages =
        pagesJson?.map((page) => PageModel.fromJson(page)).toList();

    Timestamp? timestamp;
    var timestampData = json['timestamp'];
    if (timestampData is int) {
      timestamp = Timestamp.fromMillisecondsSinceEpoch(timestampData);
    } else if (timestampData is Timestamp) {
      timestamp = timestampData;
    }

    return LessonModel(
      lastUpdated: json['lastUpdated'] as Timestamp?,
      lessonName: json['lessonName'],
      lessonId: json['lessonId'],
      lessonNo: json['lessonNo'],
      intro: json['intro'],
      audioLink: json['audioLink'],
      imageLink: json['imageLink'],
      pages: pages,
      timestamp: timestamp,
      category: json['category'],
    );
  }
  // Convert LessonModel to a map for database
  Map<String, dynamic> toMap() {
    return {
      'lessonName': lessonName,
      'lessonId': lessonId,
      'lessonNo': lessonNo,
      'intro': intro,
      'audioLink': audioLink,
      'imageLink': imageLink,
      'category': category,
    };
  }

  // Create LessonModel from a database map
  static LessonModel fromMap(Map<String, dynamic> map, List<PageModel> pages) {
    return LessonModel(
      lessonName: map['lessonName'],
      lessonId: map['lessonId'],
      lessonNo: map['lessonNo'],
      intro: map['intro'],
      audioLink: map['audioLink'],
      imageLink: map['imageLink'],
      category: map['category'],
      pages: pages,
    );
  }

  // To JSON
  Map<String, dynamic> toJson() => {
        "lessonName": lessonName,
        "lessonId": lessonId,
        "lessonNo": lessonNo,
        "intro": intro,
        "audioLink": audioLink,
        "imageLink": imageLink,
        "lastUpdated": lastUpdated ?? Timestamp.now(),
        "pages": pages?.map((page) => page.toJson()).toList(),
        "timestamp": DateTime.now().millisecondsSinceEpoch,
        "category": category,
      };

  // From Snapshot
  static LessonModel fromSnap(DocumentSnapshot snap) {
    var snapshot = snap.data() as Map<String, dynamic>;
    List<dynamic>? pagesJson = snapshot['pages'];
    List<PageModel>? pages =
        pagesJson?.map((page) => PageModel.fromJson(page)).toList();

    Timestamp? timestamp;
    var timestampData = snapshot['timestamp'];
    if (timestampData is int) {
      timestamp = Timestamp.fromMillisecondsSinceEpoch(timestampData);
    } else if (timestampData is Timestamp) {
      timestamp = timestampData;
    }

    return LessonModel(
      lessonName: snapshot['lessonName'],
      lessonId: snapshot['lessonId'],
      lessonNo: snapshot['lessonNo'],
      intro: snapshot['intro'],
      audioLink: snapshot['audioLink'],
      imageLink: snapshot['imageLink'],
      pages: pages,
      timestamp: timestamp,
      category: snapshot['category'],
    );
  }
}
