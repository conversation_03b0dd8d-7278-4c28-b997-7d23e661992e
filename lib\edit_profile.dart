import 'package:bibl/controllers/auth_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'widgets/customappbar.dart';
import 'widgets/profile_photo.dart';

class EditProfile extends StatefulWidget {
  const EditProfile({super.key});

  @override
  State<EditProfile> createState() => _EditProfileState();
}

class _EditProfileState extends State<EditProfile> {
  final AuthController authController = Get.find();
  final ProfileController profileController = Get.find();
  final nameController = TextEditingController();
  final surnameController = TextEditingController();
  final currentPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final user = FirebaseAuth.instance.currentUser;
  bool isPasswordObscure = true;
  @override
  void dispose() {
    super.dispose();
    nameController.dispose();
    surnameController.dispose();
    currentPasswordController.dispose();
    newPasswordController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: true,
        appBar: CustomAppBar(
          widget: null,
          title: 'Izmeni Profil',
        ),
        body: Column(
          children: [
            Expanded(
              child: ScrollConfiguration(
                behavior: const ScrollBehavior(),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const ProfilePhotoWidget(
                          isOnSettings: false,
                        ),
                        SizedBox(
                          height: Get.height * 0.01,
                        ),
                        const Txt(
                          txt: 'Ime',
                          fontSize: 14,
                        ),
                        const SizedBox(height: 8.0),
                        textFieldContainer(
                          context,
                          controller: nameController,
                          hint: profileController.userr.value.name ?? 'Ime',
                          prefix: const Padding(
                            padding: EdgeInsets.all(12.0),
                            child: Icon(
                              Icons.person_outline,
                              size: 25,
                            ),
                          ),
                          padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                        ),
                        SizedBox(
                          height: Get.height * 0.01,
                        ),
                        const Txt(
                          txt: 'Prezime',
                          fontSize: 14,
                        ),
                        const SizedBox(height: 8.0),
                        textFieldContainer(
                          context,
                          controller: surnameController,
                          hint: profileController.userr.value.surname ??
                              'Prezime',
                          prefix: const Padding(
                            padding: EdgeInsets.all(12.0),
                            child: Icon(
                              Icons.person_outline,
                              size: 25,
                            ),
                          ),
                          padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                        ),
                        SizedBox(
                          height: Get.height * 0.01,
                        ),
                        user!.providerData[0].providerId == 'google.com' ||
                                user!.providerData[0].providerId == 'apple.com'
                            ? const SizedBox.shrink()
                            : Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Txt(
                                    txt: 'Trenutna Lozinka',
                                    fontSize: 14,
                                  ),
                                  const SizedBox(height: 8.0),
                                  textFieldContainer(
                                    context,
                                    maxLines: 1,
                                    isObscure: isPasswordObscure ? true : null,
                                    trailing: isPasswordObscure
                                        ? GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                isPasswordObscure = false;
                                              });
                                            },
                                            child: const Padding(
                                              padding: EdgeInsets.only(
                                                  top: 10, bottom: 10),
                                              child: Icon(
                                                Icons.visibility_off_outlined,
                                                size: 20,
                                                color: greyishColor,
                                              ),
                                            ),
                                          )
                                        : GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                isPasswordObscure = true;
                                              });
                                            },
                                            child: const Padding(
                                              padding: EdgeInsets.only(
                                                  top: 10, bottom: 10),
                                              child: Icon(
                                                Icons.visibility_outlined,
                                                size: 20,
                                                color: secondaryColor,
                                              ),
                                            ),
                                          ),
                                    controller: currentPasswordController,
                                    hint: 'Trenutna Lozinka',
                                    prefix: const Padding(
                                      padding: EdgeInsets.all(12.0),
                                      child: Icon(
                                        Icons.lock_outline,
                                        color: grey2Color,
                                      ),
                                    ),
                                    padding: const EdgeInsets.fromLTRB(
                                        12, 5, 12, 15),
                                  ),
                                  SizedBox(
                                    height: Get.height * 0.01,
                                  ),
                                  const Txt(
                                    txt: 'Potvrdi Novu Lozinku',
                                    fontSize: 14,
                                  ),
                                  const SizedBox(height: 8.0),
                                  textFieldContainer(
                                    context,
                                    maxLines: 1,
                                    isObscure: isPasswordObscure ? true : null,
                                    trailing: isPasswordObscure
                                        ? GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                isPasswordObscure = false;
                                              });
                                            },
                                            child: const Padding(
                                              padding: EdgeInsets.only(
                                                  top: 10, bottom: 10),
                                              child: Icon(
                                                Icons.visibility_off_outlined,
                                                size: 20,
                                                color: greyishColor,
                                              ),
                                            ),
                                          )
                                        : GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                isPasswordObscure = true;
                                              });
                                            },
                                            child: const Padding(
                                              padding: EdgeInsets.only(
                                                  top: 10, bottom: 10),
                                              child: Icon(
                                                Icons.visibility_outlined,
                                                size: 20,
                                                color: secondaryColor,
                                              ),
                                            ),
                                          ),
                                    controller: newPasswordController,
                                    hint: 'Nova Lozinka',
                                    prefix: const Padding(
                                      padding: EdgeInsets.all(12.0),
                                      child: Icon(
                                        Icons.lock_outline,
                                        color: grey2Color,
                                      ),
                                    ),
                                    padding: const EdgeInsets.fromLTRB(
                                        12, 5, 12, 15),
                                  ),
                                ],
                              ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                top: false,
                child: Obx(
                  () => buttonContainer(
                    text: 'Sačuvaj',
                    child: profileController.isProfileUpdating.value
                        ? const Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          )
                        : null,
                    onTap: () async {
                      if (nameController.text.isEmpty &&
                          surnameController.text.isEmpty &&
                          currentPasswordController.text.isEmpty &&
                          newPasswordController.text.isEmpty) {
                      } else {
                        await profileController.updateProfile(
                            name: nameController.text,
                            surname: surnameController.text,
                            currentPassword: currentPasswordController.text,
                            newPassword: newPasswordController.text);
                        if (nameController.text.isNotEmpty ||
                            surnameController.text.isNotEmpty) {
                          Get.back();
                        }
                        nameController.clear();
                        surnameController.clear();
                        currentPasswordController.clear();
                        newPasswordController.clear();
                      }
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
