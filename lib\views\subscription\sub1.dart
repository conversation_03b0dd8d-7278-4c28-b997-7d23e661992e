import 'package:bibl/controllers/profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:bibl/widgets/custombutton.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import '../../controllers/analytics_controller.dart';
import '../../controllers/payment_controller.dart';
import '../../res/style.dart';

class SubscriptionScreen1 extends StatefulWidget {
  final bool? isBackButton;
  const SubscriptionScreen1({super.key, this.isBackButton});

  @override
  State<SubscriptionScreen1> createState() => _SubscriptionScreen1State();
}

class _SubscriptionScreen1State extends State<SubscriptionScreen1> {
  final ProfileController profileController = Get.find();
  final AnalticsController analticsController = AnalticsController();
  List<Offering> offerings = [];
  int selectedIndex = -1; // Initially no item is selected
  Package? selectedPackage;
  bool isFetchingOffers = false;
  bool isMonthlySelected = true;
  bool isFreeTimeSelected = false;

  @override
  void initState() {
    super.initState();
    selectedPackage = null;
    analticsController.subPageDisplayedAnalyticsUpdate('SubscriptionScreen');
    fetchOffers();
  }

  Future<void> fetchOffers() async {
    setState(() => isFetchingOffers = true);
    offerings = await PurchaseApi.fetchOffers();
    selectedPackage = offerings.first.availablePackages.last;
    selectedIndex = offerings.first.availablePackages.length - 1;
    setState(() => isFetchingOffers = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              _buildTabs(),
              const SizedBox(height: 16.0),
              _buildTitle(),
              const Spacer(),
              _buildOfferings(),
              Spacer(flex: MediaQuery.of(context).size.height < 700 ? 1 : 4),
              // _buildExtraTimeWidget(),
              const Spacer(),
              _buildContinueButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabs() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xff747480).withValues(alpha: 0.08),
        borderRadius: BorderRadius.circular(100),
      ),
      child: Padding(
        padding: const EdgeInsets.all(5.0),
        child: Row(
          children: [
            _buildTab('Mesečno', isMonthlySelected,
                () => setState(() => isMonthlySelected = true)),
            _buildTab('Godišnje', !isMonthlySelected,
                () => setState(() => isMonthlySelected = false)),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(String text, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? Colors.white : null,
            borderRadius: BorderRadius.circular(30.0),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 6,
                      offset: const Offset(0, 3),
                    ),
                  ]
                : null,
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Txt(
                txt: text,
                fontSize: 14,
                fontColor: isSelected
                    ? Colors.black
                    : const Color(0xff3C3C43).withValues(alpha: 0.6),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Txt(
      txt: 'Znanje je jedino što ti niko ne može oduzeti',
      fontSize: _getResponsiveFontSize(),
      maxLines: 5,
      fontColor: const Color(0xff211F20),
      textAlign: TextAlign.center,
      fontWeight: FontWeight.bold,
    );
  }

  double _getResponsiveFontSize() {
    final height = MediaQuery.of(context).size.height;
    if (height < 700) return 18;
    if (height < 800) return 24;
    if (height < 900) return 28;
    return 32;
  }

  Widget _buildOfferings() {
    if (isFetchingOffers) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: CircularProgressIndicator(color: mainColor),
      );
    }

    if (offerings.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            SizedBox(height: Get.height * 0.1),
            const Txt(
              txt: 'Trenutno nema pretplate. Molimo pokušajte ponovo kasnije.',
              maxLines: 3,
              textAlign: TextAlign.center,
              fontSize: 20,
            ),
          ],
        ),
      );
    }

    return SizedBox(
      height: MediaQuery.of(context).size.height < 700 ? 250 : 300,
      width: Get.width,
      child: Stack(
        children: [
          const ColorfulSmokyEffect(),
          _buildPriceWidget(),
        ],
      ),
    );
  }

  Widget _buildPriceWidget() {
    return Align(
      alignment: Alignment.center,
      child: SizedBox(
        height: MediaQuery.of(context).size.height < 700 ? 220 : 270,
        width: Get.width * 0.9,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.center,
              child: Container(
                height: MediaQuery.of(context).size.height < 700 ? 180 : 220,
                width: Get.width * 0.85,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFFED6A0B).withValues(alpha: 0.32),
                      const Color(0xFFAF52DE).withValues(alpha: 0.32),
                    ],
                    stops: const [0.1, 0.9],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        _buildPackageDetails(),
                        const SizedBox(height: 8),
                        _buildOfferingsList(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            ..._buildStarDecorations(),
          ],
        ),
      ),
    );
  }

  Widget _buildPackageDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(50),
            color: isMonthlySelected
                ? const Color(0xff4F9D17).withValues(alpha: 0.08)
                : const Color(0xffAF52DE).withValues(alpha: 0.08),
          ),
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 3, 16, 3),
            child: Txt(
              txt: isMonthlySelected ? 'Popularno' : 'Najpovoljnije',
              fontColor: isMonthlySelected
                  ? const Color(0xff4F9D17)
                  : const Color(0xffAF52DE),
              textAlign: TextAlign.center,
              font: 'Inter',
              fontWeight: FontWeight.w600,
              fontSize: MediaQuery.of(context).size.height < 700 ? 10 : 13,
            ),
          ),
        ),
        const SizedBox(height: 8),
        const Txt(
          txt: 'umniLab Plus',
          fontSize: 20,
          font: 'Inter',
          fontWeight: FontWeight.w600,
        ),
      ],
    );
  }

  Widget _buildOfferingsList() {
    final filteredPackages = offerings.first.availablePackages.where((package) {
      final period = package.storeProduct.defaultOption!.billingPeriod!.iso8601;
      return isMonthlySelected ? period == 'P1M' : period == 'P1Y';
    }).toList();

    return ListView.separated(
      separatorBuilder: (context, index) =>
          SizedBox(height: Get.height * 0.015),
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      reverse: true,
      itemCount: filteredPackages.length,
      itemBuilder: (context, index) =>
          _buildPackageItem(filteredPackages[index]),
    );
  }

  Widget _buildPackageItem(Package package) {
    final isYearly =
        package.storeProduct.defaultOption!.billingPeriod!.iso8601 == 'P1Y';
    final currencyCode = package.storeProduct.currencyCode;
    final price = package.storeProduct.price;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Txt(
          txt:
              'Uživaj u dodatnom premium sadržaju bez reklama, stekni znanje za manje utrošenog vremena, otkažite bilo kad.',
          maxLines: 10,
          fontWeight: FontWeight.w600,
          font: 'Inter',
          fontSize: MediaQuery.of(context).size.height < 700 ? 12 : 15,
          fontColor: const Color(0xff211F20).withValues(alpha: 0.65),
        ),
        const SizedBox(height: 8),
        Txt(
          txt: isYearly
              ? 'Samo $currencyCode${price.toStringAsFixed(0)}/Godišnje'
              : 'Samo $currencyCode${price.toStringAsFixed(0)}/Mesečno',
          maxLines: 2,
          fontWeight: FontWeight.w600,
          font: 'Inter',
          fontSize: MediaQuery.of(context).size.height < 700 ? 12 : 15,
          fontColor: const Color(0xff211F20).withValues(alpha: 0.65),
        ),
      ],
    );
  }

  List<Widget> _buildStarDecorations() {
    return [
      _buildStarDecoration(top: -15, right: 120, size: 50, blurRadius: 80),
      _buildStarDecoration(top: -10, right: 80, size: 80, blurRadius: 80),
      _buildStarDecoration(top: 50, right: -26, size: 80, blurRadius: 80),
    ];
  }

  Widget _buildStarDecoration(
      {required double top,
      required double right,
      required double size,
      required double blurRadius}) {
    return Positioned(
      top: top,
      right: right,
      child: Container(
        height: size,
        width: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Colors.yellow.withValues(alpha: 0.3),
              blurRadius: blurRadius,
              offset: const Offset(0, 0),
            ),
          ],
        ),
        child: SvgPicture.asset(
          'assets/svgs/star_icon.svg',
          height: size,
        ),
      ),
    );
  }

  Widget _buildContinueButton() {
    return buttonContainer(
      height: MediaQuery.of(context).size.height < 700 ? 50 : null,
      onTap: () async {
        if (selectedPackage == null) {
          getErrorSnackBar('Molimo vas da prvo odaberete paket');
        } else {
          await PurchaseApi.purchasePackage(
            isMonthlySelected
                ? offerings.first.availablePackages.last
                : offerings.first.availablePackages.first,
            isFreeTimeSelected,
          );
        }
      },
      text: 'Nastavi',
    );
  }
}

class ColorfulSmokyEffect extends StatelessWidget {
  const ColorfulSmokyEffect({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: Get.height * 0.5,
        width: Get.width * 0.9,
        child: CustomPaint(
          painter: SmokyGradientPainter(),
        ),
      ),
    );
  }
}

class SmokyGradientPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Rect rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final Gradient gradient = LinearGradient(
      colors: [
        const Color(0xffED6A0B).withValues(alpha: 0.32),
        const Color(0xffAF52DE).withValues(alpha: 0.32),
      ],
      stops: const [0.1, 0.9],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
    final Paint paint = Paint()
      ..shader = gradient.createShader(rect)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 50);
    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
