import 'package:bibl/controllers/auth_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../res/style.dart';
import '../../widgets/customappbar.dart';
import '../../widgets/custombutton.dart';

class EmailRegister extends StatefulWidget {
  const EmailRegister({
    super.key,
  });

  @override
  State<EmailRegister> createState() => _EmailRegisterState();
}

class _EmailRegisterState extends State<EmailRegister> {
  final nameController = TextEditingController();
  final surnameController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();

  final AuthController authController = Get.find<AuthController>();

  bool isPasswordObscure = true;
  bool isTermsChecked = false;
  @override
  void dispose() {
    nameController.dispose();
    surnameController.dispose();
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }

  void register() {
    if (surnameController.text.isEmpty ||
        passwordController.text.isEmpty ||
        confirmPasswordController.text.isEmpty ||
        nameController.text.isEmpty ||
        emailController.text.isEmpty ||
        !isTermsChecked) {
      getErrorSnackBar(errorMessage);
    } else {
      if (passwordController.text.length < 8) {
        getErrorSnackBar('Za lozinku morate imati 8 karaktera');
      } else {
        if (passwordController.text == confirmPasswordController.text) {
          authController.registerUser(
              email: emailController.text,
              password: passwordController.text,
              name: nameController.text,
              surname: surnameController.text);
        } else {
          getErrorSnackBar('Potvrda lozinke netačna');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        widget: null,
        title: 'Kreirajte profil',
      ),
      body: ScrollConfiguration(
        behavior: const ScrollBehavior(),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Txt(
                      txt: 'Ime',
                      fontSize: 14,
                    ),
                    const SizedBox(height: 8.0),
                    textFieldContainer(
                      context,
                      hint: 'Ime',
                      prefix: const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Icon(
                          Icons.person_outline,
                          size: 25,
                        ),
                      ),
                      padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                      controller: nameController,
                    ),
                    SizedBox(
                      height: Get.height * 0.01,
                    ),
                    const Txt(
                      txt: 'Prezime',
                      fontSize: 14,
                    ),
                    const SizedBox(height: 8.0),
                    textFieldContainer(
                      context,
                      hint: 'Prezime',
                      prefix: const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Icon(
                          Icons.person_outline,
                          size: 25,
                        ),
                      ),
                      padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                      controller: surnameController,
                    ),
                    SizedBox(
                      height: Get.height * 0.01,
                    ),
                    const Txt(
                      txt: 'Email',
                      fontSize: 14,
                    ),
                    const SizedBox(height: 8.0),
                    textFieldContainer(
                      context,
                      hint: 'Unesi svoju email adresu',
                      prefix: const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Icon(
                          Icons.email_outlined,
                          color: grey2Color,
                        ),
                      ),
                      padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                      controller: emailController,
                    ),
                    SizedBox(
                      height: Get.height * 0.01,
                    ),
                    const Txt(
                      txt: 'Lozinka',
                      fontSize: 14,
                    ),
                    const SizedBox(height: 8.0),
                    textFieldContainer(
                      context,
                      hint: 'Unesi željenu lozinku',
                      maxLines: 1,
                      prefix: const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Icon(
                          Icons.lock_outline,
                          color: grey2Color,
                        ),
                      ),
                      padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                      controller: passwordController,
                      isObscure: isPasswordObscure ? true : null,
                      trailing: isPasswordObscure
                          ? GestureDetector(
                              onTap: () {
                                setState(() {
                                  isPasswordObscure = false;
                                });
                              },
                              child: const Padding(
                                padding: EdgeInsets.only(top: 10, bottom: 10),
                                child: Icon(
                                  Icons.visibility_off_outlined,
                                  size: 20,
                                  color: greyishColor,
                                ),
                              ),
                            )
                          : GestureDetector(
                              onTap: () {
                                setState(() {
                                  isPasswordObscure = true;
                                });
                              },
                              child: const Padding(
                                padding: EdgeInsets.only(top: 10, bottom: 10),
                                child: Icon(
                                  Icons.visibility_outlined,
                                  size: 20,
                                  color: secondaryColor,
                                ),
                              ),
                            ),
                    ),
                    SizedBox(
                      height: Get.height * 0.01,
                    ),
                    const Txt(
                      txt: 'Potvrdi Lozinku',
                      fontSize: 14,
                    ),
                    const SizedBox(height: 8.0),
                    textFieldContainer(
                      context,
                      hint: 'Potvrdi Lozinku',
                      maxLines: 1,
                      prefix: const Padding(
                        padding: EdgeInsets.all(12.0),
                        child: Icon(
                          Icons.lock_outline,
                          color: grey2Color,
                        ),
                      ),
                      padding: const EdgeInsets.fromLTRB(12, 5, 12, 15),
                      controller: confirmPasswordController,
                      isObscure: isPasswordObscure ? true : null,
                      trailing: isPasswordObscure
                          ? GestureDetector(
                              onTap: () {
                                setState(() {
                                  isPasswordObscure = false;
                                });
                              },
                              child: const Padding(
                                padding: EdgeInsets.only(top: 10, bottom: 10),
                                child: Icon(
                                  Icons.visibility_off_outlined,
                                  size: 20,
                                  color: greyishColor,
                                ),
                              ),
                            )
                          : GestureDetector(
                              onTap: () {
                                setState(() {
                                  isPasswordObscure = true;
                                });
                              },
                              child: const Padding(
                                padding: EdgeInsets.only(top: 10, bottom: 10),
                                child: Icon(
                                  Icons.visibility_outlined,
                                  size: 20,
                                  color: secondaryColor,
                                ),
                              ),
                            ),
                    ),
                    checkBoxWidget(),
                    const SizedBox(
                      height: 20,
                    ),
                    Obx(
                      () => buttonContainer(
                          onTap: register,
                          child: authController.isAuthUpdating.isTrue
                              ? const Center(
                                  child: Padding(
                                    padding: EdgeInsets.all(5.0),
                                    child: CircularProgressIndicator(
                                        color: Colors.white),
                                  ),
                                )
                              : null,
                          text: 'Registruj se',
                          fontSize: 20),
                    ),
                    SizedBox(
                      height: Get.height * 0.07,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Row checkBoxWidget() {
    return Row(
      children: [
        Checkbox(
          value: isTermsChecked,
          activeColor: mainColor,
          onChanged: (bool? newValue) {
            setState(() {
              isTermsChecked = newValue ?? false;
            });
          },
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              isTermsChecked = !isTermsChecked;
            });
          },
          child: const Row(
            children: [
              Txt(
                txt: 'Prihvatam',
                fontSize: 12,
                fontWeight: FontWeight.normal,
              ),
              Txt(
                txt: ' Uslove korišċenja',
                fontSize: 12,
                fontWeight: FontWeight.normal,
                fontColor: mainColor,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
