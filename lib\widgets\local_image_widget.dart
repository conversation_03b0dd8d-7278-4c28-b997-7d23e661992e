import 'dart:io';
import 'package:bibl/res/style.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class LocalImageWidget extends StatefulWidget {
  final File? imageFile;

  const LocalImageWidget({Key? key, required this.imageFile}) : super(key: key);

  @override
  LocalImageWidgetState createState() => LocalImageWidgetState();
}

class LocalImageWidgetState extends State<LocalImageWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _opacityAnimation =
        Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void didUpdateWidget(LocalImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Start the fade-in once the imageFile becomes available
    if (oldWidget.imageFile == null && widget.imageFile != null) {
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.imageFile == null) {
      // Shimmer placeholder
      return Shimmer.fromColors(
        baseColor: mainColor.withValues(alpha: 0.1),
        highlightColor: Colors.white,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            color: Colors.white,
          ),
        ),
      );
    }

    return FadeTransition(
      opacity: _opacityAnimation,
      child: ClipRRect(
          borderRadius: BorderRadius.circular(14),
          child: SizedBox(
            width: double.infinity, // Ensures it takes full width
            height: double.infinity, // Ensures it takes full height
            child: Image.file(
              widget.imageFile!,
              fit: BoxFit.cover, // Ensures the image covers the entire space
            ),
          )),
    );
  }
}
