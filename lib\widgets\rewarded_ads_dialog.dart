import 'package:bibl/views/subscription/sub1.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../services/ad_mob_service.dart';
import 'cross_widget.dart';
import 'dialog_widget.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/custombutton.dart';

class RewardedAdsDialog extends StatefulWidget {
  final Widget classs;

  const RewardedAdsDialog({super.key, required this.classs});

  @override
  RewardedAdsDialogState createState() => RewardedAdsDialogState();
}

class RewardedAdsDialogState extends State<RewardedAdsDialog> {
  final RewardedAdManagerController rewardedAdManagerController = Get.find();

  @override
  void initState() {
    super.initState();
    if (!rewardedAdManagerController.isAdLoaded.value) {
      rewardedAdManagerController.loadAd();
    }
  }

  void _showRewardedAd() {
    rewardedAdManagerController.showAd(
      onReward: (reward) {
        Get.to(widget.classs);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return dialogWidget(
      Obx(
        () => Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            crossWidget(),
            SvgPicture.asset(
              'assets/svgs/rewarded_ads.svg',
              height: 150,
            ),
            const SizedBox(height: 20),
            const Txt(
              txt:
                  'Nemaš dovoljno herca da nastaviš sa sadržajem. Molimo te da izabereš jednu od dve ponuđene opcije, srećno!',
              fontSize: 16,
              maxLines: 5,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            outlineButtonContainer(
              onTap: rewardedAdManagerController.isAdLoaded.value
                  ? _showRewardedAd
                  : null,
              height: 78,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    const Expanded(
                      child: Txt(
                        txt: 'Otvori besplatno  Pogeldaj video reklamu',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        fontColor: mainColor,
                        maxLines: 2,
                      ),
                    ),
                    Container(
                        decoration: BoxDecoration(
                          gradient: rewardedAdManagerController.isAdLoaded.value
                              ? mainColorsGradient
                              : null,
                          shape: BoxShape.circle,
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(3.0),
                          child: Center(
                            child: rewardedAdManagerController.isAdLoaded.value
                                ? const Icon(
                                    Icons.play_arrow,
                                    color: Colors.white,
                                  )
                                : const CircularProgressIndicator(
                                    color: mainColor,
                                  ),
                          ),
                        ))
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10),
            outlineButtonContainer2(
              onTap: () {
                Get.back();
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: ((context) => const SubscriptionScreen1(
                              isBackButton: true,
                            ))));
              },
              child: const Padding(
                padding: EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Expanded(
                      child: Txt(
                        txt:
                            'Otključaj sav sadržaj Bez reklama, bez limitacija',
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        fontColor: Colors.white,
                        maxLines: 2,
                      ),
                    ),
                    Icon(
                      Icons.lock_open_outlined,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
