{"configurations": [{"directories": [{"build": ".", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Desktop/umniLab/android/app/.cxx/Debug/6w344a1d/arm64-v8a", "source": "C:/src/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 1}}