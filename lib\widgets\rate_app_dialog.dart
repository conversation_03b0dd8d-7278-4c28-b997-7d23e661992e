import 'dart:io';

import 'package:bibl/res/style.dart';
import 'package:bibl/widgets/feedback_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'cross_widget.dart';
import 'dialog_widget.dart';

class RateAppDialog extends StatelessWidget {
  const RateAppDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return dialogWidget(
      Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          crossWidget(),

          Image.asset(
            'assets/images/Logo Circle.png',
            height: 150,
          ),

          const SizedBox(height: 20),

          const Txt(
            txt: 'Kako ti se svidja umniLab?',
            font: 'Inter',
            textAlign: TextAlign.center,
            fontSize: 24,
            fontWeight: FontWeight.w600,
          ),

          const SizedBox(height: 10),
          const Txt(
            txt: 'Ostavite svoj utisak na App Store-u i podržite naš razvoj!',
            fontSize: 14,
            fontWeight: FontWeight.normal,
            maxLines: 3,
            fontColor: Colors.grey,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 20),

          // Thumbs Up and Down Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Thumbs Up Button

              IconButton(
                  onPressed: () async {
                    Get.back();
                    String url;

                    if (Platform.isAndroid) {
                      url = 'market://details?id=com.umniLabNewApp.umniLabApp';
                    } else {
                      url =
                          'https://apps.apple.com/app/id123456789'; // Replace with your actual App Store ID
                    }

                    if (await canLaunchUrl(Uri.parse(url))) {
                      await launchUrl(
                        Uri.parse(url),
                        mode: LaunchMode.externalApplication,
                      );
                    } else {
                      //
                    }
                  },
                  icon: SvgPicture.asset('assets/svgs/rate_thumb_icon.svg')),

              // Thumbs Down Button

              IconButton(
                onPressed: () {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    Get.back();
                    Get.dialog(const FeedbackDialog());
                  });
                },
                icon: SvgPicture.asset('assets/svgs/not_rate_thumb.svg'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
