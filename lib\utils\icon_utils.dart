import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;

class IconUtils {
  /// Validates and reformats the URL if it's from Google Drive
  static String validateSvgUrl(String url) {
    if (url.contains('drive.google.com')) {
      if (url.contains('open?id=')) {
        return url.replaceAll('open?id=', 'uc?id=');
      } else if (url.contains('/file/d/')) {
        final id = url.split('/file/d/')[1].split('/')[0];
        return 'https://drive.google.com/uc?id=$id';
      }
    }
    return url;
  }

  /// Sanitizes the file name by replacing invalid characters
  static String sanitizeFilename(String url) {
    return url.replaceAll(RegExp(r'[^\w\d]+'), '_');
  }

  /// Fetches and caches the image from the provided URL
  static Future<File?> fetchAndCacheImage(
      String imageUrl, String fileName) async {
    try {
      final sanitizedFileName = sanitizeFilename(fileName);
      final directory = await getTemporaryDirectory();
      final filePath = "${directory.path}/$sanitizedFileName";

      final localFile = File(filePath);
      if (await localFile.exists()) {
        return localFile;
      }

      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode == 200) {
        await localFile.writeAsBytes(response.bodyBytes);
        return localFile;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
