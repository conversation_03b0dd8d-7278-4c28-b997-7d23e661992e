// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';

import 'package:bibl/widgets/box_widget.dart';

import 'widgets/customappbar.dart';

class LinkLandingPage extends StatefulWidget {
  final BoxWidget widget;
  const LinkLandingPage({
    Key? key,
    required this.widget,
  }) : super(key: key);

  @override
  State<LinkLandingPage> createState() => _LinkLandingPageState();
}

class _LinkLandingPageState extends State<LinkLandingPage> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: CustomAppBar(
          height: 150.0, // Set height to 150 for other screens
          widget: null,
          isBackButton: true,
          title: 'Umnilab',
        ),
        body: widget.widget,
      ),
    );
  }
}
