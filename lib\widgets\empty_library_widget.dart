import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../res/style.dart';

Padding emptyLibraryWidget() {
  return Padding(
    padding: const EdgeInsets.all(20.0),
    child: Column(
      children: [
        // SvgPicture.asset('assets/svgs/libraryemptyIllustrations.svg'),
        SizedBox(
          height: Get.height * 0.03,
        ),
        const Txt(
          txt: 'Hajde da napunimo\ntvoju biblioteku sa znanjem!',
          maxLines: 2,
          textAlign: TextAlign.center,
          fontSize: 24,
        ),
        SizedBox(
          height: Get.height * 0.03,
        ),
        const Txt(
          txt:
              'Teme koji završi<PERSON>e se pojaviti\novde, kao i teme koje su\nzapočete.',
          maxLines: 3,
          fontColor: lightColor,
          textAlign: TextAlign.center,
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
        SizedBox(
          height: Get.height * 0.05,
        ),
      ],
    ),
  );
}
