import 'package:flutter/material.dart';
import 'package:get/get.dart';

IconButton arrowBackButtonWidget({VoidCallback? onPressed}) {
  return IconButton(
      onPressed: onPressed ??
          () {
            // Use smoother navigation - just simple back
            if (Navigator.canPop(Get.context!)) {
              Get.back();
            }
          },
      icon: CircleAvatar(
        backgroundColor: Colors.white.withValues(alpha: 0.2),
        radius: 14,
        child: const Padding(
          padding: EdgeInsets.all(5.0),
          child: Icon(
            Icons.arrow_back_ios_new,
            size: 12,
            color: Colors.white,
          ),
        ),
      ));
}
