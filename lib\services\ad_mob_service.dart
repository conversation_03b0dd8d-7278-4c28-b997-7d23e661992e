import 'dart:io';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

abstract class AdManager {
  bool isAdLoaded = false;

  void loadAd();
  void dispose();
}

class RewardedAdManagerController extends GetxController {
  Rx<bool> isAdLoaded = false.obs;
  RewardedAd? _rewardedAd;
  late void Function() onAdLoadedCallback;
  //  if (Platform.isAndroid) {
  //     return 'ca-app-pub-8639821055582439/7258439612';
  //   } else if (Platform.isIOS) {
  //     return 'ca-app-pub-8639821055582439/8607196218';
  //   }
  // static const String _adUnitId = 'ca-app-pub-3940256099942544/5224354917';
  static const String _adUnitId = 'ca-app-pub-8639821055582439/7098437682';

  @override
  void onInit() {
    super.onInit();
    loadAd();
  }

  void loadAd() {
    RewardedAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          _rewardedAd = ad;
          isAdLoaded.value = true;
          onAdLoadedCallback();
        },
        onAdFailedToLoad: (LoadAdError error) {
          //
        },
      ),
    );
  }

  void showAd({required Function(RewardItem) onReward}) {
    if (isAdLoaded.value && _rewardedAd != null) {
      _rewardedAd?.show(
          onUserEarnedReward: (AdWithoutView ad, RewardItem reward) {
        onReward(reward);
      });
      _rewardedAd = null;
      isAdLoaded.value = false;
      loadAd(); // Load a new ad after showing the current one
    } else {}
  }

  @override
  void onClose() {
    _rewardedAd?.dispose();
    super.onClose();
  }
}

class InterstitialAdManager extends AdManager {
  InterstitialAd? _interstitialAd;

  // Context: lessons or quizzes
  final String context;

  InterstitialAdManager({required this.context});

  // Dynamic Ad Unit ID based on context and platform
  String get _adUnitId {
    if (context == 'lessons') {
      return Platform.isAndroid
          ? 'ca-app-pub-8639821055582439/7747691797' // Replace with actual lessons Android ad unit ID
          : 'ca-app-pub-8639821055582439/7747691797'; // Replace with actual lessons iOS ad unit ID
    } else {
      return Platform.isAndroid
          ? 'ca-app-pub-8639821055582439/1645317870' // Replace with actual quizzes Android ad unit ID
          : 'ca-app-pub-8639821055582439/1645317870'; // Replace with actual quizzes iOS ad unit ID
    }
  }

  @override
  void loadAd() {
    InterstitialAd.load(
      adUnitId: _adUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (InterstitialAd ad) {
          _interstitialAd = ad;
          isAdLoaded = true;
        },
        onAdFailedToLoad: (LoadAdError error) {},
      ),
    );
  }

  void showAd() {
    if (isAdLoaded && _interstitialAd != null) {
      _interstitialAd?.show();
      _interstitialAd = null;
      isAdLoaded = false;
      loadAd(); // Load a new ad after showing the current one
    } else {}
  }

  @override
  void dispose() {
    _interstitialAd?.dispose();
  }
}
