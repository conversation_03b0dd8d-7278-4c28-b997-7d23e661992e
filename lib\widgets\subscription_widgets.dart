import 'package:bibl/res/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

Column subCheckBoxes(String text, BuildContext context) {
  double fontSize;

  // Adjust font size based on screen height using MediaQuery
  double screenHeight = MediaQuery.of(context).size.height;

  if (screenHeight < 600) {
    fontSize = 10;
  } else if (screenHeight < 700) {
    fontSize = 12;
  } else if (screenHeight < 800) {
    fontSize = 14;
  } else {
    fontSize = 16;
  }

  return Column(
    children: [
      SizedBox(
        height: screenHeight < 600
            ? null
            : screenHeight < 700
                ? screenHeight * 0.003
                : screenHeight < 800
                    ? screenHeight * 0.01
                    : screenHeight * 0.015,
      ),
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(3.0),
            child: Icon(
              Icons.check_circle,
              color: greenishColor,
              size: fontSize,
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Flexible(
            child: Txt(
              txt: text,
              fontWeight: FontWeight.normal,
              maxLines: 5,
              fontSize: fontSize, // Dynamically set font size
            ),
          ),
        ],
      ),
    ],
  );
}

SizedBox sub2Widget({
  required String illustration,
  required String day,
  required String text,
  required bool isLines,
}) {
  return SizedBox(
    width: Get.width * 0.7,
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            // SvgPicture.asset('assets/svgs/$illustration.svg'),
            isLines
                ? SizedBox(
                    height: 100,
                    child: RotatedBox(
                      quarterTurns: 1,
                      child: Row(
                        children: List.generate(
                            300 ~/ 10,
                            (index) => Expanded(
                                  child: Container(
                                    color: index % 2 == 0
                                        ? Colors.transparent
                                        : Colors.grey,
                                    height: 2,
                                  ),
                                )),
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        ),
        const SizedBox(
          width: 10,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Txt(
                  txt: day,
                  maxLines: 2,
                  fontColor: lightColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w600),
              Txt(
                txt: text,
                maxLines: 3,
                fontSize: 16,
              ),
            ],
          ),
        )
      ],
    ),
  );
}
