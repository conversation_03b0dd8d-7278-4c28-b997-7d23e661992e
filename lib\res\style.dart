import 'package:auto_size_text/auto_size_text.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:figma_to_flutter/figma_to_flutter.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

const MaterialColor myCustomPrimarySwatch = MaterialColor(
  0xFFAD26FF,
  <int, Color>{
    50: Color(0xFFAD26FF),
    100: Color(0xFFAD26FF),
    200: Color(0xFFAD26FF),
    300: Color(0xFFAD26FF),
    400: Color(0xFFAD26FF),
    500: Color(0xFFAD26FF),
    600: Color(0xFFAD26FF),
    700: Color(0xFFAD26FF),
    800: Color(0xFFAD26FF),
    900: Color(0xFFAD26FF),
  },
);

const Color mainColor = Color(0xFFAD26FF);
const Color mainColor2 = Color(0xFF5017F1);
const Color mainColor3 = Color(0xFF9610FF);
const Color greyColor = Color(0xFFF9F9F9);
const Color grey2Color = Color(0xFF797B82);
const Color grey3Color = Color(0xFFEEEEEE);
const Color reddishColor = Color(0xFFFF5858);
const Color yellowishColor = Color(0xFFFFFF45);
const Color lightGreenColor = Color(0xFFD9FFD2);
Color correctAnswerBorderColor = const Color(0xFF7CE099);
Color correctAnswerColor = const Color(0xFFD3FFE0).withValues(alpha: 0.5);
Color wrongAnswerBorderColor = const Color(0xFFFCA7A7);
Color wrongAnswerCircularBorderColor = const Color(0xFFFF5858);
Color correctAnswerCircularBorderColor = const Color(0xFF7CE099);
Color wrongAnswerColor = const Color(0xFFFFE7E7);

const Color greenishColor = Color(0xFF7CE099);
const Color secondaryColor = Color(0xff1E1E1E);
const Color pinkColor = Color(0xffE4B4FF);
const Color blueishColor = Color(0xff215EFD);
LinearGradient mainColorsGradient = const LinearGradient(
    begin: Alignment.bottomLeft,
    end: Alignment.topRight,
    colors: [mainColor2, mainColor]);
LinearGradient mainColorsGradient2 = LinearGradient(
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
    colors: [
      mainColor2.withValues(alpha: 0.3),
      mainColor.withValues(alpha: 0.3)
    ]);
LinearGradient weekDayTitleGradient = const LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [reddishColor, yellowishColor]);
LinearGradient onboard1Gradient = const LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xff0260FF), Color(0xff0AECFB)]);
LinearGradient onboard2Gradient = const LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xffFED23B), Color(0xffFE9E4A)]);
LinearGradient lessonImageBlackGradient = const LinearGradient(
  begin: Alignment.topCenter,
  end: Alignment.bottomCenter,
  colors: [
    Color(0xFF000000), // Black
    Color.fromRGBO(
        0, 0, 0, 0), // Transparent black (equivalent to rgba(0, 0, 0, 0))
  ],
);

const Color lightColor = Color(0xff33525B);
const Color listGreyishColor = Color(0xffF0F0F0);
const Color greyishColor = Color(0xffDADADA);
const Color darkGreyishColor = Color(0xff636F7E);

// FIREBASE
var firebaseAuth = FirebaseAuth.instance;
var firestore = FirebaseFirestore.instance;
var firebaseAnalytic = FirebaseAnalytics.instance;

//for all the text in the app
class Txt extends StatelessWidget {
  final String txt;
  final FontWeight? fontWeight;
  final FontStyle? fontStyle;
  final double fontSize;
  final Color? fontColor;
  final double? minFontSize;

  final double? lineHeight;

  // final double? letterSpacing;
  final TextOverflow? overflow;
  final TextAlign? textAlign;
  final bool? isUnderline;
  final String? font;
  final int? maxLines;
  final bool? useAutoSize;
  const Txt(
      {super.key,
      required this.txt,
      this.fontWeight,
      this.fontStyle,
      required this.fontSize,
      this.fontColor,
      this.minFontSize,
      this.lineHeight,
      // this.letterSpacing,
      this.overflow,
      this.textAlign,
      this.isUnderline,
      this.font,
      this.maxLines,
      this.useAutoSize = false});

  @override
  Widget build(BuildContext context) {
    // Use regular Text widget for better performance and no jumping
    // Only use AutoSizeText when explicitly needed
    if ((useAutoSize ?? false) && maxLines != null && maxLines! > 1) {
      return AutoSizeText(txt,
          maxLines: maxLines ?? 1,
          maxFontSize: fontSize,
          minFontSize: minFontSize ?? (fontSize * 0.8),
          textAlign: textAlign,
          style: _getTextStyle(context));
    }

    return Text(txt,
        maxLines: maxLines,
        textAlign: textAlign,
        overflow: overflow ?? TextOverflow.ellipsis,
        style: _getTextStyle(context));
  }

  TextStyle _getTextStyle(BuildContext context) {
    return TextStyle(
      fontFamily: font ?? "Poppins",
      height: lineHeight,
      fontSize: fontSize,
      decoration:
          isUnderline == null ? TextDecoration.none : TextDecoration.underline,
      fontStyle: fontStyle ?? FontStyle.normal,
      letterSpacing: -0.022 * fontSize,
      color: fontColor ?? secondaryColor,
      fontWeight: fontWeight ?? FontWeight.w500,
    );
  }
}

//for all the button texts in the app
class ButtonTxt extends StatelessWidget {
  final String txt;
  final FontWeight? fontWeight;
  final FontStyle? fontStyle;
  final double? fontSize;
  final bool? isOnlyBorder;

  final double? minFontSize;

  final double? lineHeight;

  final TextOverflow? overflow;
  final TextAlign? textAlign;
  final bool? isUnderline;
  final String? font;
  final int? maxLines;
  const ButtonTxt(
      {super.key,
      required this.txt,
      this.fontWeight,
      this.isOnlyBorder,
      this.fontStyle,
      this.fontSize,
      this.minFontSize,
      this.lineHeight,
      this.overflow,
      this.textAlign,
      this.isUnderline,
      this.font,
      this.maxLines});

  @override
  Widget build(BuildContext context) {
    // Use regular Text widget for buttons to prevent jumping
    return Text(txt,
        maxLines: maxLines ?? 1,
        textAlign: textAlign,
        overflow: overflow ?? TextOverflow.ellipsis,
        style: TextStyle(
          fontFamily: font ?? 'Poppins',
          height: lineHeight,
          fontSize: fontSize ?? 16,
          letterSpacing: -0.41,
          color: isOnlyBorder != null ? mainColor : Colors.white,
          fontWeight: fontWeight ?? FontWeight.w600,
          fontStyle: fontStyle ?? FontStyle.normal,
          decoration: isUnderline == null
              ? TextDecoration.none
              : TextDecoration.underline,
        ));
  }
}

Widget whiteContainer(
  BuildContext context, {
  double? height,
  double? width,
  Color? color,
  Color? borderColor,
  String? text,
  Widget? child,
  double? borderWidth,
  bool? isSharpBorders,
  bool? isThereBorders,
}) {
  return Container(
      width: width ?? Get.width * 0.9,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15.0),
        color: color ?? Colors.white,
        border: Border.all(
            width: borderWidth ?? 0, color: borderColor ?? Colors.transparent),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.16),
            offset: const Offset(0, 3.0),
            blurRadius: 6.0,
          ),
        ],
      ),
      child: child);
}

Widget textFieldContainer(BuildContext context,
    {TextEditingController? controller,
    String? hint,
    String? labelText,
    bool? isObscure,
    bool? isEnabled,
    bool? isAutoFocus,
    double? height,
    Widget? trailing,
    EdgeInsets? padding,
    FocusNode? focusNode,
    Widget? prefix,
    List<TextInputFormatter>? inputFormatter,
    Function(String)? onChanged,
    String? Function(String?)? validator,
    bool? isOnlyNumberField,
    bool? isTextAlignCenter,
    int? maxLines}) {
  return SizedBox(
    height: height ?? 58,
    child: TextFormField(
      textAlignVertical: TextAlignVertical.center,
      textAlign: isTextAlignCenter != null ? TextAlign.center : TextAlign.left,
      focusNode: focusNode,
      enabled: isEnabled == null
          ? true
          : isEnabled
              ? true
              : false,
      inputFormatters: inputFormatter ?? [],
      keyboardType:
          isOnlyNumberField == null ? TextInputType.text : TextInputType.number,
      obscureText: isObscure == null ? false : true,
      onChanged: onChanged,
      autofocus: isAutoFocus ?? false,
      maxLines: maxLines,
      validator: validator ??
          (val) {
            if (val!.isEmpty) {
              return 'This field is required';
            } else {
              return null;
            }
          },
      // Use consistent text styling to prevent jumping
      style: const TextStyle(
        fontFamily: "Poppins",
        fontSize: 14,
        letterSpacing: -0.0022,
        color: secondaryColor,
        fontWeight: FontWeight.w400,
      ),
      controller: controller,
      decoration: InputDecoration(
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(50),
          borderSide: const BorderSide(
            color: greyishColor,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(50),
          borderSide: const BorderSide(
            color: greyishColor,
          ),
        ),
        contentPadding: padding ?? const EdgeInsets.all(8),
        isDense: false,
        suffixIcon: trailing,
        labelText: labelText,
        labelStyle: const TextStyle(
          fontFamily: "Poppins",
          fontSize: 14,
          letterSpacing: -0.0022,
          color: lightColor,
          fontWeight: FontWeight.w400,
        ),
        prefixIcon: prefix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(50),
        ),
        hintText: hint,
        hintStyle: const TextStyle(
          fontFamily: "Poppins",
          fontSize: 14,
          letterSpacing: -0.0022,
          color: lightColor,
          fontWeight: FontWeight.w400,
        ),
      ),
    ),
  );
}

getErrorSnackBar(String message) {
  Get.snackbar(
    'Došlo je do greške',
    message,
    titleText: const Txt(
        txt: 'Došlo je do greške',
        fontSize: 22,
        fontColor: Colors.white,
        fontWeight: FontWeight.bold),
    messageText:
        Txt(txt: message, fontSize: 14, maxLines: 2, fontColor: Colors.white),
    snackPosition: SnackPosition.BOTTOM,
    duration: const Duration(seconds: 2),
    backgroundColor: Colors.red.shade300,
    borderRadius: 0,
    margin: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
  );
}

getSuccessSnackBar(String message) {
  Get.snackbar(
    'Uspešno!',
    message,
    titleText: const Txt(
        txt: 'Uspešno!',
        fontSize: 22,
        fontColor: Colors.white,
        fontWeight: FontWeight.bold),
    messageText:
        Txt(txt: message, fontSize: 14, maxLines: 2, fontColor: Colors.white),
    snackPosition: SnackPosition.BOTTOM,
    duration: const Duration(seconds: 3),
    backgroundColor: Colors.green.shade300,
    borderRadius: 0,
    margin: const EdgeInsets.only(bottom: 0, left: 0, right: 0),
  );
}

String errorMessage = "Molimo popunite sve detalje";
String smthgwentWrong = "Nešto je pošlo po zlu, Molimo pokušajte ponovo";
